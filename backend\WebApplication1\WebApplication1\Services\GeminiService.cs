using System.Text;
using System.Text.Json;
using WebApplication1.DTOs;
using WebApplication1.Data;
using Microsoft.EntityFrameworkCore;
using AutoMapper;

namespace WebApplication1.Services
{
    public class GeminiService : IGeminiService
    {
        private readonly HttpClient _httpClient;
        private readonly IConfiguration _configuration;
        private readonly ApplicationDbContext _context;
        private readonly IMapper _mapper;
        private readonly string _apiKey;
        private readonly string _baseUrl;
        private readonly string _model;

        public GeminiService(HttpClient httpClient, IConfiguration configuration, ApplicationDbContext context, IMapper mapper)
        {
            _httpClient = httpClient;
            _configuration = configuration;
            _context = context;
            _mapper = mapper;
            _apiKey = _configuration["GeminiSettings:ApiKey"] ?? throw new ArgumentNullException("Gemini API key not configured");
            _baseUrl = _configuration["GeminiSettings:BaseUrl"] ?? "https://generativelanguage.googleapis.com/v1beta";
            _model = _configuration["GeminiSettings:Model"] ?? "gemini-1.5-flash";
        }

        public async Task<SimilarQuestionsResponseDto> FindSimilarQuestionsAsync(string title, string body, List<string> tags)
        {
            try
            {
                // Get existing questions from database
                var existingQuestions = await _context.Questions
                    .Include(q => q.User)
                    .Include(q => q.QuestionTags)
                        .ThenInclude(qt => qt.Tag)
                    .Include(q => q.Answers)
                    .Include(q => q.Votes)
                    .OrderByDescending(q => q.CreatedAt)
                    .Take(50) // Limit to recent questions for performance
                    .ToListAsync();

                if (!existingQuestions.Any())
                {
                    return new SimilarQuestionsResponseDto
                    {
                        HasSimilarQuestions = false,
                        SuggestionMessage = "No existing questions found to compare against."
                    };
                }

                var similarQuestions = new List<SimilarQuestionDto>();

                // Use Gemini to analyze similarity for each question
                foreach (var question in existingQuestions)
                {
                    var isSimlar = await IsQuestionSimilarAsync(title, body, question.Title, question.Body);
                    
                    if (isSimlar)
                    {
                        var similarQuestion = new SimilarQuestionDto
                        {
                            Id = question.Id,
                            Title = question.Title,
                            Body = question.Body.Length > 200 ? question.Body.Substring(0, 200) + "..." : question.Body,
                            CreatedAt = question.CreatedAt,
                            User = _mapper.Map<UserDto>(question.User),
                            Tags = question.QuestionTags.Select(qt => _mapper.Map<TagDto>(qt.Tag)).ToList(),
                            AnswerCount = question.Answers.Count,
                            VoteCount = question.Votes.Sum(v => v.IsUpvote ? 1 : -1),
                            SimilarityScore = 0.8 // Placeholder - could be enhanced with actual scoring
                        };
                        similarQuestions.Add(similarQuestion);
                    }

                    // Limit to top 5 similar questions to avoid overwhelming the user
                    if (similarQuestions.Count >= 5)
                        break;
                }

                return new SimilarQuestionsResponseDto
                {
                    HasSimilarQuestions = similarQuestions.Any(),
                    SimilarQuestions = similarQuestions.OrderByDescending(q => q.SimilarityScore).ToList(),
                    SuggestionMessage = similarQuestions.Any() 
                        ? "We found some similar questions. Please check if any of these answer your question before posting."
                        : "Your question appears to be unique. Feel free to post it!"
                };
            }
            catch (Exception ex)
            {
                // Log the error but don't fail the question creation process
                Console.WriteLine($"Error in similarity check: {ex.Message}");
                return new SimilarQuestionsResponseDto
                {
                    HasSimilarQuestions = false,
                    SuggestionMessage = "Unable to check for similar questions at this time. You can still post your question."
                };
            }
        }

        public async Task<bool> IsQuestionSimilarAsync(string newTitle, string newBody, string existingTitle, string existingBody)
        {
            try
            {
                var prompt = $@"
Compare these two programming questions and determine if they are asking about the same or very similar problems.

Question 1:
Title: {newTitle}
Body: {newBody}

Question 2:
Title: {existingTitle}
Body: {existingBody}

Respond with only 'true' if the questions are similar (asking about the same core problem, concept, or issue), or 'false' if they are different.
Consider questions similar if they:
- Ask about the same programming concept or technology
- Have the same core problem even with different wording
- Would likely have overlapping answers

Respond with only 'true' or 'false', nothing else.";

                var requestBody = new
                {
                    contents = new[]
                    {
                        new
                        {
                            parts = new[]
                            {
                                new { text = prompt }
                            }
                        }
                    }
                };

                var json = JsonSerializer.Serialize(requestBody);
                var content = new StringContent(json, Encoding.UTF8, "application/json");

                var url = $"{_baseUrl}/models/{_model}:generateContent?key={_apiKey}";
                var response = await _httpClient.PostAsync(url, content);

                if (response.IsSuccessStatusCode)
                {
                    var responseContent = await response.Content.ReadAsStringAsync();
                    var geminiResponse = JsonSerializer.Deserialize<GeminiResponse>(responseContent);
                    
                    var text = geminiResponse?.candidates?.FirstOrDefault()?.content?.parts?.FirstOrDefault()?.text?.Trim().ToLower();
                    return text == "true";
                }

                return false;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error calling Gemini API: {ex.Message}");
                return false;
            }
        }
    }

    // Helper classes for Gemini API response
    public class GeminiResponse
    {
        public GeminiCandidate[]? candidates { get; set; }
    }

    public class GeminiCandidate
    {
        public GeminiContent? content { get; set; }
    }

    public class GeminiContent
    {
        public GeminiPart[]? parts { get; set; }
    }

    public class GeminiPart
    {
        public string? text { get; set; }
    }
}
