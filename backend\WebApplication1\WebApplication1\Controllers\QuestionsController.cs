using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using System.Security.Claims;
using AutoMapper;
using WebApplication1.Data;
using WebApplication1.DTOs;
using WebApplication1.Models;
using WebApplication1.Services;

namespace WebApplication1.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    public class QuestionsController : ControllerBase
    {
        private readonly ApplicationDbContext _context;
        private readonly IMapper _mapper;
        private readonly IGeminiService _geminiService;

        public QuestionsController(ApplicationDbContext context, IMapper mapper, IGeminiService geminiService)
        {
            _context = context;
            _mapper = mapper;
            _geminiService = geminiService;
        }

        [HttpGet]
        public async Task<ActionResult<PaginatedResponseDto<QuestionSummaryDto>>> GetQuestions([FromQuery] QuestionFiltersDto filters)
        {
            try
            {
                var query = _context.Questions
                    .Include(q => q.User)
                    .Include(q => q.QuestionTags)
                        .ThenInclude(qt => qt.Tag)
                    .Include(q => q.Answers)
                    .Include(q => q.Votes)
                    .AsQueryable();

                // Apply search filter
                if (!string.IsNullOrEmpty(filters.Search))
                {
                    var searchTerm = filters.Search.ToLower();
                    query = query.Where(q => q.Title.ToLower().Contains(searchTerm) ||
                                           q.Body.ToLower().Contains(searchTerm));
                }

                // Apply tag filter
                if (filters.Tags != null && filters.Tags.Any())
                {
                    query = query.Where(q => q.QuestionTags.Any(qt => filters.Tags.Contains(qt.Tag.Name)));
                }

                // Apply sorting
                query = filters.SortBy.ToLower() switch
                {
                    "votes" => query.OrderByDescending(q => q.Votes.Sum(v => v.IsUpvote ? 1 : -1)),
                    "activity" => query.OrderByDescending(q => q.Answers.Any() ?
                        q.Answers.Max(a => a.CreatedAt) : q.CreatedAt),
                    _ => query.OrderByDescending(q => q.CreatedAt) // newest (default)
                };

                var total = await query.CountAsync();
                var totalPages = (int)Math.Ceiling((double)total / filters.Limit);

                var questions = await query
                    .Skip((filters.Page - 1) * filters.Limit)
                    .Take(filters.Limit)
                    .ToListAsync();

                var questionDtos = _mapper.Map<List<QuestionSummaryDto>>(questions);

                // Set user vote information if user is authenticated
                var currentUserId = GetCurrentUserId();
                if (currentUserId.HasValue)
                {
                    foreach (var questionDto in questionDtos)
                    {
                        var question = questions.First(q => q.Id == questionDto.Id);
                        var userVote = question.Votes.FirstOrDefault(v => v.UserId == currentUserId.Value);
                        questionDto.UserVote = userVote?.IsUpvote == true ? "up" :
                                             userVote?.IsUpvote == false ? "down" : null;
                    }
                }

                return Ok(new PaginatedResponseDto<QuestionSummaryDto>
                {
                    Data = questionDtos,
                    Total = total,
                    Page = filters.Page,
                    Limit = filters.Limit,
                    TotalPages = totalPages
                });
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { message = "An error occurred while fetching questions", details = ex.Message });
            }
        }

        [HttpGet("{id}")]
        public async Task<ActionResult<QuestionDto>> GetQuestion(int id)
        {
            try
            {
                var question = await _context.Questions
                    .Include(q => q.User)
                    .Include(q => q.QuestionTags)
                        .ThenInclude(qt => qt.Tag)
                    .Include(q => q.Answers)
                        .ThenInclude(a => a.User)
                    .Include(q => q.Answers)
                        .ThenInclude(a => a.Votes)
                    .Include(q => q.Votes)
                    .FirstOrDefaultAsync(q => q.Id == id);

                if (question == null)
                {
                    return NotFound(new { message = "Question not found" });
                }

                var questionDto = _mapper.Map<QuestionDto>(question);

                // Set user vote information if user is authenticated
                var currentUserId = GetCurrentUserId();
                if (currentUserId.HasValue)
                {
                    var userVote = question.Votes.FirstOrDefault(v => v.UserId == currentUserId.Value);
                    questionDto.UserVote = userVote?.IsUpvote == true ? "up" :
                                         userVote?.IsUpvote == false ? "down" : null;

                    // Set user vote for answers
                    foreach (var answerDto in questionDto.Answers)
                    {
                        var answer = question.Answers.First(a => a.Id == answerDto.Id);
                        var answerUserVote = answer.Votes.FirstOrDefault(v => v.UserId == currentUserId.Value);
                        answerDto.UserVote = answerUserVote?.IsUpvote == true ? "up" :
                                           answerUserVote?.IsUpvote == false ? "down" : null;
                    }
                }

                return Ok(questionDto);
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { message = "An error occurred while fetching the question", details = ex.Message });
            }
        }

        [HttpPost]
        [Authorize]
        public async Task<ActionResult<QuestionDto>> CreateQuestion([FromBody] CreateQuestionRequestDto createQuestionRequest)
        {
            try
            {
                var currentUserId = GetCurrentUserId();
                if (!currentUserId.HasValue)
                {
                    return Unauthorized();
                }

                var question = new Question
                {
                    Title = createQuestionRequest.Title,
                    Body = createQuestionRequest.Body,
                    UserId = currentUserId.Value,
                    CreatedAt = DateTime.UtcNow
                };

                _context.Questions.Add(question);
                await _context.SaveChangesAsync();

                // Handle tags
                foreach (var tagName in createQuestionRequest.Tags)
                {
                    var tag = await _context.Tags.FirstOrDefaultAsync(t => t.Name == tagName.ToLower());
                    if (tag == null)
                    {
                        tag = new Tag { Name = tagName.ToLower() };
                        _context.Tags.Add(tag);
                        await _context.SaveChangesAsync();
                    }

                    var questionTag = new QuestionTag
                    {
                        QuestionId = question.Id,
                        TagId = tag.Id
                    };
                    _context.QuestionTags.Add(questionTag);
                }

                await _context.SaveChangesAsync();

                // Reload question with all related data
                var createdQuestion = await _context.Questions
                    .Include(q => q.User)
                    .Include(q => q.QuestionTags)
                        .ThenInclude(qt => qt.Tag)
                    .Include(q => q.Answers)
                    .Include(q => q.Votes)
                    .FirstOrDefaultAsync(q => q.Id == question.Id);

                var questionDto = _mapper.Map<QuestionDto>(createdQuestion);
                return CreatedAtAction(nameof(GetQuestion), new { id = question.Id }, questionDto);
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { message = "An error occurred while creating the question", details = ex.Message });
            }
        }

        [HttpPost("check-similarity")]
        [Authorize]
        public async Task<ActionResult<SimilarQuestionsResponseDto>> CheckSimilarity([FromBody] SimilarQuestionCheckDto request)
        {
            try
            {
                var result = await _geminiService.FindSimilarQuestionsAsync(request.Title, request.Body, request.Tags);
                return Ok(result);
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { message = "An error occurred while checking for similar questions", details = ex.Message });
            }
        }

        private int? GetCurrentUserId()
        {
            var userIdClaim = User.FindFirst(ClaimTypes.NameIdentifier);
            return userIdClaim != null ? int.Parse(userIdClaim.Value) : null;
        }
    }
}
