import {
  Question,
  QuestionSummary,
  CreateQuestionRequest,
  UpdateQuestionRequest,
  QuestionFilters,
  PaginatedResponse,
} from "../types";
import { mockApiService } from "./mockApiService";

// Always use real API
const USE_MOCK_API = false;

export const questionService = {
  async getQuestions(
    filters?: QuestionFilters
  ): Promise<PaginatedResponse<QuestionSummary>> {
    if (USE_MOCK_API) {
      return mockApiService.getQuestions(filters);
    }
    // Real API implementation
    const { apiClient } = await import("./api");
    return apiClient.get<PaginatedResponse<QuestionSummary>>(
      "/questions",
      filters
    );
  },

  async getQuestionById(id: number): Promise<Question> {
    if (USE_MOCK_API) {
      return mockApiService.getQuestionById(id);
    }
    // Real API implementation
    const { apiClient } = await import("./api");
    return apiClient.get<Question>(`/questions/${id}`);
  },

  async createQuestion(questionData: CreateQuestionRequest): Promise<Question> {
    if (USE_MOCK_API) {
      return mockApiService.createQuestion(questionData);
    }
    // Real API implementation
    const { apiClient } = await import("./api");
    return apiClient.post<Question>("/questions", questionData);
  },

  async updateQuestion(
    id: number,
    questionData: UpdateQuestionRequest
  ): Promise<Question> {
    if (USE_MOCK_API) {
      throw new Error("Mock API does not support question updates");
    }
    // Real API implementation would go here
    throw new Error("Real API not implemented");
  },

  async deleteQuestion(id: number): Promise<void> {
    if (USE_MOCK_API) {
      throw new Error("Mock API does not support question deletion");
    }
    // Real API implementation would go here
    throw new Error("Real API not implemented");
  },

  async searchQuestions(
    query: string,
    filters?: Omit<QuestionFilters, "search">
  ): Promise<PaginatedResponse<QuestionSummary>> {
    if (USE_MOCK_API) {
      return mockApiService.getQuestions({
        search: query,
        ...filters,
      });
    }
    // Real API implementation
    const { apiClient } = await import("./api");
    return apiClient.get<PaginatedResponse<QuestionSummary>>("/questions", {
      search: query,
      ...filters,
    });
  },

  async getUserQuestions(userId?: number): Promise<QuestionSummary[]> {
    const { apiClient } = await import("./api");
    const endpoint = userId
      ? `/questions/user/${userId}`
      : "/questions/user/my-questions";
    return apiClient.get<QuestionSummary[]>(endpoint);
  },
};
