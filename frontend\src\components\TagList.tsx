import React from 'react';
import { motion } from 'framer-motion';
import { Tag } from '../types';

interface TagListProps {
  tags: Tag[];
  onTagClick?: (tag: Tag) => void;
  className?: string;
}

const TagList: React.FC<TagListProps> = ({ tags, onTagClick, className = '' }) => {
  return (
    <div className={`flex flex-wrap gap-2 ${className}`}>
      {tags.map((tag) => (
        <motion.span
          key={tag.id}
          whileHover={{ scale: 1.05 }}
          onClick={() => onTagClick?.(tag)}
          className={`inline-block px-3 py-1 text-sm bg-blue-100 text-blue-800 rounded-full ${
            onTagClick ? 'cursor-pointer hover:bg-blue-200' : ''
          } transition-colors duration-200`}
        >
          {tag.name}
        </motion.span>
      ))}
    </div>
  );
};

export default TagList;
