import React from 'react';
import { motion } from 'framer-motion';

interface VoteButtonsProps {
  voteCount: number;
  userVote: 'up' | 'down' | null;
  onVote: (isUpvote: boolean) => void;
  disabled?: boolean;
}

const VoteButtons: React.FC<VoteButtonsProps> = ({
  voteCount,
  userVote,
  onVote,
  disabled = false,
}) => {
  return (
    <div className="flex flex-col items-center space-y-1">
      <motion.button
        whileHover={!disabled ? { scale: 1.1 } : {}}
        whileTap={!disabled ? { scale: 0.9 } : {}}
        onClick={() => !disabled && onVote(true)}
        disabled={disabled}
        className={`p-2 rounded-full transition-colors duration-200 ${
          userVote === 'up'
            ? 'text-orange-500 bg-orange-50'
            : 'text-gray-400 hover:text-orange-500 hover:bg-orange-50'
        } ${disabled ? 'cursor-not-allowed opacity-50' : 'cursor-pointer'}`}
      >
        <svg className="w-6 h-6" fill="currentColor" viewBox="0 0 24 24">
          <path d="M12 4l8 8h-6v8h-4v-8H4l8-8z" />
        </svg>
      </motion.button>
      
      <span className={`text-lg font-semibold ${
        voteCount > 0 ? 'text-green-600' : 
        voteCount < 0 ? 'text-red-600' : 
        'text-gray-600'
      }`}>
        {voteCount}
      </span>
      
      <motion.button
        whileHover={!disabled ? { scale: 1.1 } : {}}
        whileTap={!disabled ? { scale: 0.9 } : {}}
        onClick={() => !disabled && onVote(false)}
        disabled={disabled}
        className={`p-2 rounded-full transition-colors duration-200 ${
          userVote === 'down'
            ? 'text-red-500 bg-red-50'
            : 'text-gray-400 hover:text-red-500 hover:bg-red-50'
        } ${disabled ? 'cursor-not-allowed opacity-50' : 'cursor-pointer'}`}
      >
        <svg className="w-6 h-6" fill="currentColor" viewBox="0 0 24 24">
          <path d="M12 20l-8-8h6V4h4v8h6l-8 8z" />
        </svg>
      </motion.button>
    </div>
  );
};

export default VoteButtons;
