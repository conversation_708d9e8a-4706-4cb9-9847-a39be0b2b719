import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { Answer } from '../types';
import VoteButtons from './VoteButtons';
import { voteService } from '../services/voteService';
import { answerService } from '../services/answerService';
import { useAuth } from '../contexts/AuthContext';

interface AnswerCardProps {
  answer: Answer;
  onVoteUpdate?: (answerId: string, newVoteCount: number, userVote: 'up' | 'down' | null) => void;
  onAnswerUpdate?: (answerId: string, newBody: string) => void;
  onAnswerDelete?: (answerId: string) => void;
}

const AnswerCard: React.FC<AnswerCardProps> = ({
  answer,
  onVoteUpdate,
  onAnswerUpdate,
  onAnswerDelete,
}) => {
  const { user, isAuthenticated } = useAuth();
  const [isEditing, setIsEditing] = useState(false);
  const [editBody, setEditBody] = useState(answer.body);
  const [isLoading, setIsLoading] = useState(false);

  const isOwner = user?.id === answer.userId;

  const handleVote = async (isUpvote: boolean) => {
    if (!isAuthenticated) return;

    try {
      const result = await voteService.voteOnAnswer(answer.id, isUpvote);
      onVoteUpdate?.(answer.id, result.voteCount, result.userVote);
    } catch (error) {
      console.error('Error voting on answer:', error);
    }
  };

  const handleEdit = async () => {
    if (!editBody.trim()) return;

    setIsLoading(true);
    try {
      await answerService.updateAnswer(answer.id, { body: editBody });
      onAnswerUpdate?.(answer.id, editBody);
      setIsEditing(false);
    } catch (error) {
      console.error('Error updating answer:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleDelete = async () => {
    if (!window.confirm('Are you sure you want to delete this answer?')) return;

    setIsLoading(true);
    try {
      await answerService.deleteAnswer(answer.id);
      onAnswerDelete?.(answer.id);
    } catch (error) {
      console.error('Error deleting answer:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className="bg-white rounded-lg border border-gray-200 p-6"
    >
      <div className="flex space-x-4">
        {/* Vote Buttons */}
        <div className="flex-shrink-0">
          <VoteButtons
            voteCount={answer.voteCount}
            userVote={answer.userVote || null}
            onVote={handleVote}
            disabled={!isAuthenticated}
          />
        </div>

        {/* Answer Content */}
        <div className="flex-1 min-w-0">
          {isEditing ? (
            <div className="space-y-4">
              <textarea
                value={editBody}
                onChange={(e) => setEditBody(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-transparent"
                rows={6}
                placeholder="Write your answer..."
              />
              <div className="flex space-x-2">
                <button
                  onClick={handleEdit}
                  disabled={isLoading || !editBody.trim()}
                  className="px-4 py-2 bg-orange-500 text-white rounded-lg hover:bg-orange-600 disabled:opacity-50 disabled:cursor-not-allowed transition-colors duration-200"
                >
                  {isLoading ? 'Saving...' : 'Save'}
                </button>
                <button
                  onClick={() => {
                    setIsEditing(false);
                    setEditBody(answer.body);
                  }}
                  className="px-4 py-2 bg-gray-300 text-gray-700 rounded-lg hover:bg-gray-400 transition-colors duration-200"
                >
                  Cancel
                </button>
              </div>
            </div>
          ) : (
            <>
              <div className="prose max-w-none">
                <p className="text-gray-800 whitespace-pre-wrap">{answer.body}</p>
              </div>

              {/* Answer Meta */}
              <div className="mt-4 flex items-center justify-between">
                <div className="flex items-center space-x-4 text-sm text-gray-500">
                  {isOwner && (
                    <div className="flex space-x-2">
                      <button
                        onClick={() => setIsEditing(true)}
                        className="text-orange-600 hover:text-orange-700 transition-colors duration-200"
                      >
                        Edit
                      </button>
                      <button
                        onClick={handleDelete}
                        disabled={isLoading}
                        className="text-red-600 hover:text-red-700 transition-colors duration-200"
                      >
                        Delete
                      </button>
                    </div>
                  )}
                </div>

                <div className="flex items-center space-x-2 text-sm text-gray-500">
                  <span>answered {formatDate(answer.createdAt)}</span>
                  <span>by</span>
                  <span className="font-medium text-orange-600">{answer.user.username}</span>
                </div>
              </div>
            </>
          )}
        </div>
      </div>
    </motion.div>
  );
};

export default AnswerCard;
