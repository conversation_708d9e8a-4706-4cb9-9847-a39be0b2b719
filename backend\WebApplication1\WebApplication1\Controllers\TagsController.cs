using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using AutoMapper;
using WebApplication1.Data;
using WebApplication1.DTOs;

namespace WebApplication1.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    public class TagsController : ControllerBase
    {
        private readonly ApplicationDbContext _context;
        private readonly IMapper _mapper;

        public TagsController(ApplicationDbContext context, IMapper mapper)
        {
            _context = context;
            _mapper = mapper;
        }

        [HttpGet]
        public async Task<ActionResult<List<TagDto>>> GetTags([FromQuery] string? search = null)
        {
            try
            {
                var query = _context.Tags.AsQueryable();

                if (!string.IsNullOrEmpty(search))
                {
                    query = query.Where(t => t.Name.Contains(search.ToLower()));
                }

                var tags = await query
                    .OrderBy(t => t.Name)
                    .ToListAsync();

                var tagDtos = _mapper.Map<List<TagDto>>(tags);
                return Ok(tagDtos);
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { message = "An error occurred while fetching tags", details = ex.Message });
            }
        }
    }
}
