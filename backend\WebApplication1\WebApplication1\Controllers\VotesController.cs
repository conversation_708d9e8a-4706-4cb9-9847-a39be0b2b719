using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using System.Security.Claims;
using WebApplication1.Data;
using WebApplication1.DTOs;
using WebApplication1.Models;

namespace WebApplication1.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    [Authorize]
    public class VotesController : ControllerBase
    {
        private readonly ApplicationDbContext _context;

        public VotesController(ApplicationDbContext context)
        {
            _context = context;
        }

        [HttpPost("question")]
        public async Task<ActionResult<VoteResponseDto>> VoteOnQuestion([FromBody] VoteRequestDto voteRequest)
        {
            try
            {
                var currentUserId = GetCurrentUserId();
                if (!currentUserId.HasValue)
                {
                    return Unauthorized();
                }

                if (!voteRequest.QuestionId.HasValue)
                {
                    return BadRequest(new { message = "QuestionId is required" });
                }

                // Verify question exists
                var question = await _context.Questions
                    .Include(q => q.Votes)
                    .FirstOrDefaultAsync(q => q.Id == voteRequest.QuestionId.Value);

                if (question == null)
                {
                    return NotFound(new { message = "Question not found" });
                }

                // Check if user already voted
                var existingVote = await _context.Votes
                    .FirstOrDefaultAsync(v => v.UserId == currentUserId.Value && v.QuestionId == voteRequest.QuestionId.Value);

                if (existingVote != null)
                {
                    if (existingVote.IsUpvote == voteRequest.IsUpvote)
                    {
                        // Remove vote if clicking same button
                        _context.Votes.Remove(existingVote);
                    }
                    else
                    {
                        // Change vote
                        existingVote.IsUpvote = voteRequest.IsUpvote;
                    }
                }
                else
                {
                    // Add new vote
                    var newVote = new Vote
                    {
                        UserId = currentUserId.Value,
                        QuestionId = voteRequest.QuestionId.Value,
                        IsUpvote = voteRequest.IsUpvote,
                        CreatedAt = DateTime.UtcNow
                    };
                    _context.Votes.Add(newVote);
                }

                await _context.SaveChangesAsync();

                // Calculate new vote count and user vote
                var votes = await _context.Votes
                    .Where(v => v.QuestionId == voteRequest.QuestionId.Value)
                    .ToListAsync();

                var voteCount = votes.Sum(v => v.IsUpvote ? 1 : -1);
                var userVote = votes.FirstOrDefault(v => v.UserId == currentUserId.Value);

                return Ok(new VoteResponseDto
                {
                    VoteCount = voteCount,
                    UserVote = userVote?.IsUpvote == true ? "up" : 
                              userVote?.IsUpvote == false ? "down" : null
                });
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { message = "An error occurred while voting", details = ex.Message });
            }
        }

        [HttpPost("answer")]
        public async Task<ActionResult<VoteResponseDto>> VoteOnAnswer([FromBody] VoteRequestDto voteRequest)
        {
            try
            {
                var currentUserId = GetCurrentUserId();
                if (!currentUserId.HasValue)
                {
                    return Unauthorized();
                }

                if (!voteRequest.AnswerId.HasValue)
                {
                    return BadRequest(new { message = "AnswerId is required" });
                }

                // Verify answer exists
                var answer = await _context.Answers
                    .Include(a => a.Votes)
                    .FirstOrDefaultAsync(a => a.Id == voteRequest.AnswerId.Value);

                if (answer == null)
                {
                    return NotFound(new { message = "Answer not found" });
                }

                // Check if user already voted
                var existingVote = await _context.Votes
                    .FirstOrDefaultAsync(v => v.UserId == currentUserId.Value && v.AnswerId == voteRequest.AnswerId.Value);

                if (existingVote != null)
                {
                    if (existingVote.IsUpvote == voteRequest.IsUpvote)
                    {
                        // Remove vote if clicking same button
                        _context.Votes.Remove(existingVote);
                    }
                    else
                    {
                        // Change vote
                        existingVote.IsUpvote = voteRequest.IsUpvote;
                    }
                }
                else
                {
                    // Add new vote
                    var newVote = new Vote
                    {
                        UserId = currentUserId.Value,
                        AnswerId = voteRequest.AnswerId.Value,
                        IsUpvote = voteRequest.IsUpvote,
                        CreatedAt = DateTime.UtcNow
                    };
                    _context.Votes.Add(newVote);
                }

                await _context.SaveChangesAsync();

                // Calculate new vote count and user vote
                var votes = await _context.Votes
                    .Where(v => v.AnswerId == voteRequest.AnswerId.Value)
                    .ToListAsync();

                var voteCount = votes.Sum(v => v.IsUpvote ? 1 : -1);
                var userVote = votes.FirstOrDefault(v => v.UserId == currentUserId.Value);

                return Ok(new VoteResponseDto
                {
                    VoteCount = voteCount,
                    UserVote = userVote?.IsUpvote == true ? "up" : 
                              userVote?.IsUpvote == false ? "down" : null
                });
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { message = "An error occurred while voting", details = ex.Message });
            }
        }

        private int? GetCurrentUserId()
        {
            var userIdClaim = User.FindFirst(ClaimTypes.NameIdentifier);
            return userIdClaim != null ? int.Parse(userIdClaim.Value) : null;
        }
    }
}
