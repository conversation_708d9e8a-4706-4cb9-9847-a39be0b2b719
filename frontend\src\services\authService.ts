import { AuthResponse, LoginRequest, RegisterRequest } from "../types";
import { mockApiService } from "./mockApiService";

// Always use real API
const USE_MOCK_API = false;

export const authService = {
  async login(credentials: LoginRequest): Promise<AuthResponse> {
    if (USE_MOCK_API) {
      return mockApiService.login(credentials);
    }
    // Real API implementation
    const { apiClient } = await import("./api");
    return apiClient.post<AuthResponse>("/auth/login", credentials);
  },

  async register(userData: RegisterRequest): Promise<AuthResponse> {
    if (USE_MOCK_API) {
      return mockApiService.register(userData);
    }
    // Real API implementation
    const { apiClient } = await import("./api");
    return apiClient.post<AuthResponse>("/auth/register", userData);
  },

  async refreshToken(): Promise<AuthResponse> {
    if (USE_MOCK_API) {
      throw new Error("Mock API does not support token refresh");
    }
    // Real API implementation would go here
    throw new Error("Real API not implemented");
  },

  async logout(): Promise<void> {
    if (USE_MOCK_API) {
      mockApiService.setCurrentUser(null);
      return;
    }
    // Real API implementation would go here
    throw new Error("Real API not implemented");
  },
};
