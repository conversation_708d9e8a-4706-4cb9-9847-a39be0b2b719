import { CreateQuestionRequest } from "../types";

export interface SimilarQuestion {
  id: number;
  title: string;
  body: string;
  createdAt: string;
  user: {
    id: number;
    username: string;
    email: string;
    createdAt: string;
    reputation: number;
  };
  tags: Array<{
    id: number;
    name: string;
  }>;
  answerCount: number;
  voteCount: number;
  similarityScore: number;
}

export interface SimilarQuestionsResponse {
  hasSimilarQuestions: boolean;
  similarQuestions: SimilarQuestion[];
  suggestionMessage: string;
}

export const geminiService = {
  async checkSimilarity(questionData: CreateQuestionRequest): Promise<SimilarQuestionsResponse> {
    try {
      const { apiClient } = await import("./api");
      const response = await apiClient.post<SimilarQuestionsResponse>(
        "/questions/check-similarity",
        questionData
      );
      return response;
    } catch (error) {
      console.error("Error checking question similarity:", error);
      // Return a safe fallback response
      return {
        hasSimilarQuestions: false,
        similarQuestions: [],
        suggestionMessage: "Unable to check for similar questions at this time. You can still post your question."
      };
    }
  }
};
