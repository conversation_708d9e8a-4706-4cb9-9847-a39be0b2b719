import React, { useState, useEffect } from "react";
import { motion } from "framer-motion";
import { useAuth } from "../contexts/AuthContext";
import { QuestionSummary, Answer, UserProfile } from "../types";
import { questionService } from "../services/questionService";
import { answerService } from "../services/answerService";
import { userService } from "../services/userService";
import QuestionCard from "../components/QuestionCard";

const Profile: React.FC = () => {
  const { user, isAuthenticated } = useAuth();
  const [userQuestions, setUserQuestions] = useState<QuestionSummary[]>([]);
  const [userAnswers, setUserAnswers] = useState<Answer[]>([]);
  const [userProfile, setUserProfile] = useState<UserProfile | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [activeTab, setActiveTab] = useState<"questions" | "answers">(
    "questions"
  );

  useEffect(() => {
    if (isAuthenticated) {
      loadUserData();
    }
  }, [isAuthenticated]);

  const loadUserData = async () => {
    setIsLoading(true);
    try {
      // Load all user data in parallel
      const [profile, questions, answers] = await Promise.all([
        userService.getUserProfile(),
        questionService.getUserQuestions(),
        answerService.getUserAnswers(),
      ]);

      setUserProfile(profile);
      setUserQuestions(questions);
      setUserAnswers(answers);
    } catch (error) {
      console.error("Error loading user data:", error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleVoteUpdate = (
    questionId: string,
    newVoteCount: number,
    userVote: "up" | "down" | null
  ) => {
    setUserQuestions((prev) =>
      prev.map((q) =>
        q.id === questionId ? { ...q, voteCount: newVoteCount, userVote } : q
      )
    );
  };

  if (!isAuthenticated || !user) {
    return (
      <div className="text-center py-12">
        <div className="text-gray-500 text-lg">
          Please log in to view your profile.
        </div>
      </div>
    );
  }

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString("en-US", {
      year: "numeric",
      month: "long",
      day: "numeric",
    });
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      exit={{ opacity: 0, y: -20 }}
      transition={{ duration: 0.3 }}
      className="max-w-6xl mx-auto space-y-6"
    >
      {/* Profile Header */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-8">
        <div className="flex items-center space-x-6">
          <div className="w-20 h-20 bg-orange-500 text-white rounded-full flex items-center justify-center text-3xl font-bold">
            {user.username.charAt(0).toUpperCase()}
          </div>

          <div className="flex-1">
            <h1 className="text-3xl font-bold text-gray-900">
              {user.username}
            </h1>
            <p className="text-gray-600 mt-1">{user.email}</p>
            <p className="text-sm text-gray-500 mt-2">
              Member since {formatDate(user.createdAt)}
            </p>
          </div>
        </div>

        {/* Stats */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mt-8 pt-8 border-t border-gray-200">
          <div className="text-center">
            <div className="text-2xl font-bold text-orange-600">
              {userProfile?.questionCount || userQuestions.length}
            </div>
            <div className="text-gray-600">Questions</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-green-600">
              {userProfile?.answerCount || userAnswers.length}
            </div>
            <div className="text-gray-600">Answers</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-blue-600">
              {userProfile?.reputation || user.reputation}
            </div>
            <div className="text-gray-600">Reputation</div>
          </div>
        </div>
      </div>

      {/* Content Tabs */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200">
        {/* Tab Headers */}
        <div className="flex border-b border-gray-200">
          <button
            onClick={() => setActiveTab("questions")}
            className={`px-6 py-4 font-medium text-sm border-b-2 transition-colors duration-200 ${
              activeTab === "questions"
                ? "border-orange-500 text-orange-600"
                : "border-transparent text-gray-500 hover:text-gray-700"
            }`}
          >
            Questions ({userProfile?.questionCount || userQuestions.length})
          </button>
          <button
            onClick={() => setActiveTab("answers")}
            className={`px-6 py-4 font-medium text-sm border-b-2 transition-colors duration-200 ${
              activeTab === "answers"
                ? "border-orange-500 text-orange-600"
                : "border-transparent text-gray-500 hover:text-gray-700"
            }`}
          >
            Answers ({userProfile?.answerCount || userAnswers.length})
          </button>
        </div>

        {/* Tab Content */}
        <div className="p-6">
          {activeTab === "questions" && (
            <div>
              {isLoading ? (
                <div className="flex justify-center items-center py-12">
                  <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-orange-500"></div>
                </div>
              ) : userQuestions.length === 0 ? (
                <div className="text-center py-12">
                  <div className="text-gray-500 text-lg mb-4">
                    You haven't asked any questions yet.
                  </div>
                  <button
                    onClick={() => (window.location.href = "/ask")}
                    className="px-6 py-3 bg-orange-500 text-white rounded-lg hover:bg-orange-600 transition-colors duration-200"
                  >
                    Ask Your First Question
                  </button>
                </div>
              ) : (
                <div className="space-y-4">
                  {userQuestions.map((question) => (
                    <QuestionCard
                      key={question.id}
                      question={question}
                      onVoteUpdate={handleVoteUpdate}
                    />
                  ))}
                </div>
              )}
            </div>
          )}

          {activeTab === "answers" && (
            <div>
              {isLoading ? (
                <div className="flex justify-center items-center py-12">
                  <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-orange-500"></div>
                </div>
              ) : userAnswers.length === 0 ? (
                <div className="text-center py-12">
                  <div className="text-gray-500 text-lg">
                    You haven't answered any questions yet.
                  </div>
                </div>
              ) : (
                <div className="space-y-4">
                  {userAnswers.map((answer) => (
                    <div
                      key={answer.id}
                      className="border border-gray-200 rounded-lg p-4"
                    >
                      <div className="flex justify-between items-start mb-2">
                        <h3 className="text-lg font-semibold text-blue-600 hover:text-blue-800">
                          <a href={`/questions/${answer.question?.id}`}>
                            {answer.question?.title}
                          </a>
                        </h3>
                        <span className="text-sm text-gray-500">
                          {formatDate(answer.createdAt)}
                        </span>
                      </div>
                      <div className="text-gray-700 mb-3">
                        {answer.body.length > 200
                          ? answer.body.substring(0, 200) + "..."
                          : answer.body}
                      </div>
                      <div className="flex items-center space-x-4 text-sm text-gray-500">
                        <span>{answer.voteCount} votes</span>
                        {answer.question?.tags && (
                          <div className="flex space-x-1">
                            {answer.question.tags.slice(0, 3).map((tag) => (
                              <span
                                key={tag.id}
                                className="px-2 py-1 bg-gray-100 text-gray-700 rounded text-xs"
                              >
                                {tag.name}
                              </span>
                            ))}
                          </div>
                        )}
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </div>
          )}
        </div>
      </div>
    </motion.div>
  );
};

export default Profile;
