using System.ComponentModel.DataAnnotations;

namespace WebApplication1.DTOs
{
    public class CreateQuestionRequestDto
    {
        [Required]
        [StringLength(200, MinimumLength = 10)]
        public string Title { get; set; } = string.Empty;

        [Required]
        [MinLength(30)]
        public string Body { get; set; } = string.Empty;

        [Required]
        [MinLength(1)]
        [MaxLength(5)]
        public List<string> Tags { get; set; } = new List<string>();
    }

    public class UpdateQuestionRequestDto
    {
        [StringLength(200, MinimumLength = 10)]
        public string? Title { get; set; }

        [MinLength(30)]
        public string? Body { get; set; }

        [MaxLength(5)]
        public List<string>? Tags { get; set; }
    }

    public class QuestionDto
    {
        public int Id { get; set; }
        public string Title { get; set; } = string.Empty;
        public string Body { get; set; } = string.Empty;
        public DateTime CreatedAt { get; set; }
        public DateTime? UpdatedAt { get; set; }
        public int UserId { get; set; }
        public UserDto User { get; set; } = null!;
        public List<TagDto> Tags { get; set; } = new List<TagDto>();
        public List<AnswerDto> Answers { get; set; } = new List<AnswerDto>();
        public int VoteCount { get; set; }
        public string? UserVote { get; set; } // "up", "down", or null
    }

    public class QuestionSummaryDto
    {
        public int Id { get; set; }
        public string Title { get; set; } = string.Empty;
        public string Body { get; set; } = string.Empty;
        public DateTime CreatedAt { get; set; }
        public DateTime? UpdatedAt { get; set; }
        public UserDto User { get; set; } = null!;
        public List<TagDto> Tags { get; set; } = new List<TagDto>();
        public int AnswerCount { get; set; }
        public int VoteCount { get; set; }
        public string? UserVote { get; set; } // "up", "down", or null
    }

    public class QuestionFiltersDto
    {
        public string? Search { get; set; }
        public List<string>? Tags { get; set; }
        public string SortBy { get; set; } = "newest"; // "newest", "votes", "activity"
        public int Page { get; set; } = 1;
        public int Limit { get; set; } = 10;
    }

    public class PaginatedResponseDto<T>
    {
        public List<T> Data { get; set; } = new List<T>();
        public int Total { get; set; }
        public int Page { get; set; }
        public int Limit { get; set; }
        public int TotalPages { get; set; }
    }
}
