{"ConnectionStrings": {"DefaultConnection": "Data Source=LAPTOP-NPARJA5M\\SQLEXPRESS;Initial Catalog=StackOverflowClone;Persist Security Info=True;User ID=sa;Password=*****;TrustServerCertificate=True;MultipleActiveResultSets=true"}, "JwtSettings": {"SecretKey": "YourSuperSecretKeyThatIsAtLeast32CharactersLong!@#$%^&*()", "Issuer": "StackOverflowCloneAPI", "Audience": "StackOverflowCloneClient", "ExpiryInHours": "24"}, "GeminiSettings": {"ApiKey": "AIzaSyBybfIFFHm4ujHwrajqddYAtbNw50HfNL4", "BaseUrl": "https://generativelanguage.googleapis.com/v1beta", "Model": "gemini-1.5-flash"}, "Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning", "Microsoft.EntityFrameworkCore.Database.Command": "Warning"}}, "AllowedHosts": "*"}