export interface UserProfile {
  id: number;
  username: string;
  email: string;
  createdAt: string;
  reputation: number;
  questionCount: number;
  answerCount: number;
}

export const userService = {
  async getUserProfile(userId?: number): Promise<UserProfile> {
    const { apiClient } = await import("./api");
    const endpoint = userId ? `/users/${userId}` : "/users/profile";
    return apiClient.get<UserProfile>(endpoint);
  },
};
