# Stack Overflow Clone - Backend API

A comprehensive Stack Overflow clone backend API built with ASP.NET Core, Entity Framework, and SQL Server.

## 🚀 Features

### ✅ Implemented Features
- **JWT Authentication** - Secure user registration and login
- **Question Management** - CRUD operations for questions
- **Answer System** - Add, edit, and delete answers
- **Voting System** - Upvote/downvote questions and answers
- **Tag Management** - Create and search tags
- **Database Relationships** - Proper foreign keys and constraints
- **API Documentation** - Swagger/OpenAPI integration
- **CORS Support** - Frontend integration ready

### 🎨 Tech Stack
- **ASP.NET Core 8** - Web API framework
- **Entity Framework Core** - ORM for database operations
- **SQL Server** - Database (LocalDB for development)
- **JWT Bearer Authentication** - Secure token-based auth
- **AutoMapper** - Object-to-object mapping
- **BCrypt** - Password hashing
- **Swagger/OpenAPI** - API documentation

## 🏗️ Architecture

### Database Schema
```
Users (Id, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>ash, CreatedAt)
Questions (Id, Title, Body, CreatedAt, UpdatedAt, UserId)
Answers (Id, Body, CreatedAt, UpdatedAt, UserId, QuestionId)
Tags (Id, Name)
Votes (Id, UserId, QuestionId?, AnswerId?, IsUpvote, CreatedAt)
QuestionTags (Id, QuestionId, TagId)
```

### API Endpoints

#### Authentication
- `POST /api/auth/login` - User login
- `POST /api/auth/register` - User registration

#### Questions
- `GET /api/questions` - Get paginated questions with filters
- `GET /api/questions/{id}` - Get question details with answers
- `POST /api/questions` - Create new question (auth required)

#### Answers
- `POST /api/questions/{id}/answers` - Add answer (auth required)
- `PUT /api/answers/{id}` - Update answer (auth required, owner only)
- `DELETE /api/answers/{id}` - Delete answer (auth required, owner only)

#### Votes
- `POST /api/votes/question` - Vote on question (auth required)
- `POST /api/votes/answer` - Vote on answer (auth required)

#### Tags
- `GET /api/tags` - Get all tags with optional search

## 🏃‍♂️ Getting Started

### Prerequisites
- .NET 8 SDK
- SQL Server or SQL Server Express LocalDB
- Visual Studio 2022 or VS Code

### Installation

1. **Clone and navigate to backend:**
   ```bash
   cd backend/WebApplication1/WebApplication1
   ```

2. **Restore packages:**
   ```bash
   dotnet restore
   ```

3. **Update connection string (if needed):**
   Edit `appsettings.json` and update the connection string:
   ```json
   {
     "ConnectionStrings": {
       "DefaultConnection": "Server=(localdb)\\mssqllocaldb;Database=StackOverflowCloneDB;Trusted_Connection=true;MultipleActiveResultSets=true"
     }
   }
   ```

4. **Run the application:**
   ```bash
   dotnet run
   ```

5. **Access the API:**
   - API: `https://localhost:7xxx` (check console output)
   - Swagger UI: `https://localhost:7xxx` (root URL)

## 📊 Sample Data

The application automatically seeds the database with sample data:

### Sample Users
All users have password: `password123`
- **john_doe** (<EMAIL>)
- **jane_smith** (<EMAIL>)
- **dev_master** (<EMAIL>)
- **react_ninja** (<EMAIL>)
- **code_wizard** (<EMAIL>)

### Sample Questions
1. "How to use React hooks effectively in functional components?"
2. "Best practices for implementing authentication in React applications?"
3. "Should I use TypeScript with React? What are the benefits?"
4. "How to optimize React app performance for large datasets?"
5. "React Router v6 - How to implement nested routes properly?"

### Sample Tags
javascript, react, typescript, nodejs, css, html, python, api, database, authentication, frontend, backend, hooks, state-management, routing

## 🔧 Configuration

### JWT Settings
Update `appsettings.json` for JWT configuration:
```json
{
  "JwtSettings": {
    "SecretKey": "YourSuperSecretKeyThatIsAtLeast32CharactersLong!@#$%^&*()",
    "Issuer": "StackOverflowCloneAPI",
    "Audience": "StackOverflowCloneClient",
    "ExpiryInHours": "24"
  }
}
```

### CORS Settings
The API is configured to allow requests from:
- `http://localhost:5173` (Vite dev server)
- `http://localhost:3000` (Create React App dev server)

## 🧪 Testing the API

### Using Swagger UI
1. Run the application
2. Navigate to the root URL (Swagger UI)
3. Test endpoints directly from the browser

### Authentication Flow
1. **Register a new user:**
   ```json
   POST /api/auth/register
   {
     "username": "testuser",
     "email": "<EMAIL>",
     "password": "password123"
   }
   ```

2. **Login to get JWT token:**
   ```json
   POST /api/auth/login
   {
     "email": "<EMAIL>",
     "password": "password123"
   }
   ```

3. **Use token in Authorization header:**
   ```
   Authorization: Bearer <your-jwt-token>
   ```

### Sample API Calls

#### Get Questions with Filters
```bash
GET /api/questions?search=react&sortBy=votes&page=1&limit=10
```

#### Create a Question
```bash
POST /api/questions
Authorization: Bearer <token>
Content-Type: application/json

{
  "title": "How to handle state in React?",
  "body": "I'm confused about state management...",
  "tags": ["react", "javascript", "state-management"]
}
```

#### Vote on a Question
```bash
POST /api/votes/question
Authorization: Bearer <token>
Content-Type: application/json

{
  "questionId": 1,
  "isUpvote": true
}
```

## 📁 Project Structure

```
WebApplication1/
├── Controllers/          # API controllers
│   ├── AuthController.cs
│   ├── QuestionsController.cs
│   ├── AnswersController.cs
│   ├── VotesController.cs
│   └── TagsController.cs
├── Models/              # Entity models
│   ├── User.cs
│   ├── Question.cs
│   ├── Answer.cs
│   ├── Tag.cs
│   ├── Vote.cs
│   └── QuestionTag.cs
├── DTOs/               # Data Transfer Objects
│   ├── AuthDTOs.cs
│   ├── QuestionDTOs.cs
│   ├── AnswerDTOs.cs
│   ├── VoteDTOs.cs
│   └── TagDTOs.cs
├── Data/               # Database context and seeding
│   ├── ApplicationDbContext.cs
│   └── DatabaseSeeder.cs
├── Services/           # Business logic services
│   ├── IAuthService.cs
│   └── AuthService.cs
├── Mappings/           # AutoMapper profiles
│   └── MappingProfile.cs
├── Program.cs          # Application startup
└── appsettings.json    # Configuration
```

## 🔒 Security Features

- **Password Hashing** - BCrypt for secure password storage
- **JWT Authentication** - Stateless token-based authentication
- **Authorization** - Protected endpoints require valid JWT
- **CORS** - Configured for frontend integration
- **Input Validation** - Data annotations and model validation
- **SQL Injection Protection** - Entity Framework parameterized queries

## 🚀 Deployment

### For Production
1. Update connection string for production database
2. Change JWT secret key to a secure random value
3. Update CORS origins for production frontend URL
4. Set `ASPNETCORE_ENVIRONMENT=Production`
5. Use proper SSL certificates

### Database Migrations (Optional)
If you want to use migrations instead of EnsureCreated:
```bash
dotnet ef migrations add InitialCreate
dotnet ef database update
```

## 🔮 Future Enhancements

- User reputation system
- Question categories
- Comment system on answers
- Email notifications
- File upload for images
- Advanced search with Elasticsearch
- Rate limiting
- Caching with Redis
- Admin panel endpoints

## 📝 Notes

- Database is created automatically on first run
- Sample data is seeded automatically
- Swagger UI is available at the root URL in development
- All passwords in sample data are "password123"
- JWT tokens expire after 24 hours (configurable)
