import React, { useState, useEffect } from "react";
import { useParams, useNavigate } from "react-router-dom";
import { motion } from "framer-motion";
import { Question, Answer, CreateAnswerRequest } from "../types";
import { questionService } from "../services/questionService";
import { answerService } from "../services/answerService";
import { voteService } from "../services/voteService";
import { useAuth } from "../contexts/AuthContext";
import VoteButtons from "../components/VoteButtons";
import TagList from "../components/TagList";
import AnswerCard from "../components/AnswerCard";

const QuestionDetail: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const { user, isAuthenticated } = useAuth();

  const [question, setQuestion] = useState<Question | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [answerBody, setAnswerBody] = useState("");
  const [isSubmittingAnswer, setIsSubmittingAnswer] = useState(false);
  const [answerError, setAnswerError] = useState<string>("");

  useEffect(() => {
    if (id) {
      loadQuestion();
    }
  }, [id]);

  const loadQuestion = async () => {
    if (!id) return;

    const questionId = parseInt(id);
    if (isNaN(questionId)) {
      setError("Invalid question ID");
      setIsLoading(false);
      return;
    }

    setIsLoading(true);
    setError(null);

    try {
      const questionData = await questionService.getQuestionById(questionId);
      setQuestion(questionData);
    } catch (err: any) {
      setError(err.message || "Failed to load question");
    } finally {
      setIsLoading(false);
    }
  };

  const handleQuestionVote = async (isUpvote: boolean) => {
    if (!question || !isAuthenticated) return;

    try {
      const result = await voteService.voteOnQuestion(question.id, isUpvote);
      setQuestion((prev) =>
        prev
          ? {
              ...prev,
              voteCount: result.voteCount,
              userVote: result.userVote,
            }
          : null
      );
    } catch (error) {
      console.error("Error voting on question:", error);
    }
  };

  const handleAnswerVoteUpdate = (
    answerId: number,
    newVoteCount: number,
    userVote: "up" | "down" | null
  ) => {
    setQuestion((prev) =>
      prev
        ? {
            ...prev,
            answers: prev.answers.map((answer) =>
              answer.id === answerId
                ? { ...answer, voteCount: newVoteCount, userVote }
                : answer
            ),
          }
        : null
    );
  };

  const handleAnswerUpdate = (answerId: number, newBody: string) => {
    setQuestion((prev) =>
      prev
        ? {
            ...prev,
            answers: prev.answers.map((answer) =>
              answer.id === answerId ? { ...answer, body: newBody } : answer
            ),
          }
        : null
    );
  };

  const handleAnswerDelete = (answerId: number) => {
    setQuestion((prev) =>
      prev
        ? {
            ...prev,
            answers: prev.answers.filter((answer) => answer.id !== answerId),
          }
        : null
    );
  };

  const handleSubmitAnswer = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!question || !isAuthenticated) return;

    // Validate answer length
    if (!answerBody.trim()) {
      setAnswerError("Answer is required");
      return;
    }

    if (answerBody.trim().length < 30) {
      setAnswerError("Answer must be at least 30 characters long");
      return;
    }

    setAnswerError("");
    setIsSubmittingAnswer(true);
    try {
      const answerData: CreateAnswerRequest = {
        body: answerBody.trim(),
        questionId: question.id,
      };

      const newAnswer = await answerService.createAnswer(answerData);
      setQuestion((prev) =>
        prev
          ? {
              ...prev,
              answers: [...prev.answers, newAnswer],
            }
          : null
      );
      setAnswerBody("");
    } catch (error: any) {
      console.error("Error submitting answer:", error);
      setAnswerError(
        error.message || "Failed to submit answer. Please try again."
      );
    } finally {
      setIsSubmittingAnswer(false);
    }
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString("en-US", {
      year: "numeric",
      month: "long",
      day: "numeric",
      hour: "2-digit",
      minute: "2-digit",
    });
  };

  if (isLoading) {
    return (
      <div className="flex justify-center items-center min-h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-orange-500"></div>
      </div>
    );
  }

  if (error || !question) {
    return (
      <div className="text-center py-12">
        <div className="text-red-600 text-lg">
          {error || "Question not found"}
        </div>
        <button
          onClick={() => navigate("/")}
          className="mt-4 px-4 py-2 bg-orange-500 text-white rounded-lg hover:bg-orange-600 transition-colors duration-200"
        >
          Back to Questions
        </button>
      </div>
    );
  }

  const isQuestionOwner = user?.id === question.userId;

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      exit={{ opacity: 0, y: -20 }}
      transition={{ duration: 0.3 }}
      className="max-w-5xl mx-auto space-y-6"
    >
      {/* Question */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-8">
        <div className="flex space-x-6">
          {/* Vote Buttons */}
          <div className="flex-shrink-0">
            <VoteButtons
              voteCount={question.voteCount}
              userVote={question.userVote || null}
              onVote={handleQuestionVote}
              disabled={!isAuthenticated}
            />
          </div>

          {/* Question Content */}
          <div className="flex-1 min-w-0">
            <h1 className="text-3xl font-bold text-gray-900 mb-4">
              {question.title}
            </h1>

            <div className="prose max-w-none mb-6">
              <p className="text-gray-800 whitespace-pre-wrap">
                {question.body}
              </p>
            </div>

            {/* Tags */}
            {question.tags.length > 0 && (
              <div className="mb-6">
                <TagList tags={question.tags} />
              </div>
            )}

            {/* Question Meta */}
            <div className="flex items-center justify-between text-sm text-gray-500 border-t pt-4">
              <div className="flex items-center space-x-4">
                {isQuestionOwner && (
                  <div className="flex space-x-2">
                    <button className="text-orange-600 hover:text-orange-700 transition-colors duration-200">
                      Edit
                    </button>
                    <button className="text-red-600 hover:text-red-700 transition-colors duration-200">
                      Delete
                    </button>
                  </div>
                )}
              </div>

              <div className="flex items-center space-x-2">
                <span>asked {formatDate(question.createdAt)}</span>
                <span>by</span>
                <span className="font-medium text-orange-600">
                  {question.user.username}
                </span>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Answers Section */}
      <div className="space-y-6">
        <h2 className="text-2xl font-bold text-gray-900">
          {question.answers.length} Answer
          {question.answers.length !== 1 ? "s" : ""}
        </h2>

        {question.answers.map((answer) => (
          <AnswerCard
            key={answer.id}
            answer={answer}
            onVoteUpdate={handleAnswerVoteUpdate}
            onAnswerUpdate={handleAnswerUpdate}
            onAnswerDelete={handleAnswerDelete}
          />
        ))}
      </div>

      {/* Answer Form */}
      {isAuthenticated ? (
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-8">
          <h3 className="text-xl font-bold text-gray-900 mb-4">Your Answer</h3>

          <form onSubmit={handleSubmitAnswer} className="space-y-4">
            <div>
              <textarea
                value={answerBody}
                onChange={(e) => {
                  setAnswerBody(e.target.value);
                  if (answerError) setAnswerError("");
                }}
                className={`w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-transparent ${
                  answerError ? "border-red-300" : "border-gray-300"
                }`}
                rows={8}
                placeholder="Write your answer here... (minimum 30 characters)"
                required
              />
              <div className="flex justify-between items-center mt-2">
                <div className="text-sm text-gray-500">
                  {answerBody.length}/30 characters minimum
                </div>
                {answerError && (
                  <div className="text-sm text-red-600">{answerError}</div>
                )}
              </div>
            </div>

            <motion.button
              whileHover={{ scale: 1.02 }}
              whileTap={{ scale: 0.98 }}
              type="submit"
              disabled={isSubmittingAnswer || !answerBody.trim()}
              className="px-6 py-3 bg-orange-500 text-white rounded-lg hover:bg-orange-600 disabled:opacity-50 disabled:cursor-not-allowed transition-colors duration-200"
            >
              {isSubmittingAnswer ? (
                <div className="flex items-center">
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                  Posting...
                </div>
              ) : (
                "Post Your Answer"
              )}
            </motion.button>
          </form>
        </div>
      ) : (
        <div className="bg-gray-50 rounded-lg border border-gray-200 p-8 text-center">
          <p className="text-gray-600 mb-4">
            You must be logged in to post an answer.
          </p>
          <button
            onClick={() =>
              navigate("/login", {
                state: { from: { pathname: `/questions/${id}` } },
              })
            }
            className="px-6 py-3 bg-orange-500 text-white rounded-lg hover:bg-orange-600 transition-colors duration-200"
          >
            Login to Answer
          </button>
        </div>
      )}
    </motion.div>
  );
};

export default QuestionDetail;
