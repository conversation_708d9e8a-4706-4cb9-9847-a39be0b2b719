import React from "react";
import { <PERSON> } from "react-router-dom";
import { motion } from "framer-motion";
import { Question, QuestionSummary } from "../types";
import TagList from "./TagList";
import VoteButtons from "./VoteButtons";
import { voteService } from "../services/voteService";
import { useAuth } from "../contexts/AuthContext";

interface QuestionCardProps {
  question: Question | QuestionSummary;
  onVoteUpdate?: (
    questionId: number,
    newVoteCount: number,
    userVote: "up" | "down" | null
  ) => void;
}

const QuestionCard: React.FC<QuestionCardProps> = ({
  question,
  onVoteUpdate,
}) => {
  const { isAuthenticated } = useAuth();

  // Type guard to check if question has answers array or answerCount
  const getAnswerCount = (q: Question | QuestionSummary): number => {
    if ("answers" in q && q.answers) {
      return q.answers.length;
    }
    if ("answerCount" in q) {
      return q.answerCount;
    }
    return 0;
  };

  const handleVote = async (isUpvote: boolean) => {
    if (!isAuthenticated) return;

    try {
      const result = await voteService.voteOnQuestion(question.id, isUpvote);
      onVoteUpdate?.(question.id, result.voteCount, result.userVote);
    } catch (error) {
      console.error("Error voting on question:", error);
    }
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString("en-US", {
      year: "numeric",
      month: "short",
      day: "numeric",
    });
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      whileHover={{ y: -2 }}
      className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 hover:shadow-md transition-shadow duration-200"
    >
      <div className="flex space-x-4">
        {/* Vote Buttons */}
        <div className="flex-shrink-0">
          <VoteButtons
            voteCount={question.voteCount}
            userVote={question.userVote || null}
            onVote={handleVote}
            disabled={!isAuthenticated}
          />
        </div>

        {/* Question Content */}
        <div className="flex-1 min-w-0">
          <div className="flex items-start justify-between">
            <div className="flex-1">
              <Link
                to={`/questions/${question.id}`}
                className="text-xl font-semibold text-gray-900 hover:text-orange-600 transition-colors duration-200"
              >
                {question.title}
              </Link>

              <p className="mt-2 text-gray-600 line-clamp-3">
                {question.body.length > 200
                  ? `${question.body.substring(0, 200)}...`
                  : question.body}
              </p>
            </div>
          </div>

          {/* Tags */}
          {question.tags.length > 0 && (
            <div className="mt-4">
              <TagList tags={question.tags} />
            </div>
          )}

          {/* Meta Information */}
          <div className="mt-4 flex items-center justify-between text-sm text-gray-500">
            <div className="flex items-center space-x-4">
              <span className="flex items-center">
                <svg
                  className="w-4 h-4 mr-1"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"
                  />
                </svg>
                {getAnswerCount(question)} answers
              </span>
            </div>

            <div className="flex items-center space-x-2">
              <span>asked {formatDate(question.createdAt)}</span>
              <span>by</span>
              <span className="font-medium text-orange-600">
                {question.user.username}
              </span>
            </div>
          </div>
        </div>
      </div>
    </motion.div>
  );
};

export default QuestionCard;
