using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using System.Security.Claims;
using AutoMapper;
using WebApplication1.Data;
using WebApplication1.DTOs;
using WebApplication1.Models;

namespace WebApplication1.Controllers
{
    [ApiController]
    [Route("api")]
    public class AnswersController : ControllerBase
    {
        private readonly ApplicationDbContext _context;
        private readonly IMapper _mapper;

        public AnswersController(ApplicationDbContext context, IMapper mapper)
        {
            _context = context;
            _mapper = mapper;
        }

        [HttpPost("questions/{questionId}/answers")]
        [Authorize]
        public async Task<ActionResult<AnswerDto>> CreateAnswer(int questionId, [FromBody] CreateAnswerRequestDto createAnswerRequest)
        {
            try
            {
                var currentUserId = GetCurrentUserId();
                if (!currentUserId.HasValue)
                {
                    return Unauthorized();
                }

                // Verify question exists
                var question = await _context.Questions.FindAsync(questionId);
                if (question == null)
                {
                    return NotFound(new { message = "Question not found" });
                }

                var answer = _mapper.Map<Answer>(createAnswerRequest);
                answer.UserId = currentUserId.Value;
                answer.QuestionId = questionId;

                _context.Answers.Add(answer);
                await _context.SaveChangesAsync();

                // Reload answer with user data
                var createdAnswer = await _context.Answers
                    .Include(a => a.User)
                    .Include(a => a.Votes)
                    .FirstOrDefaultAsync(a => a.Id == answer.Id);

                var answerDto = _mapper.Map<AnswerDto>(createdAnswer);
                return Ok(answerDto);
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { message = "An error occurred while creating the answer", details = ex.Message });
            }
        }

        [HttpPut("answers/{id}")]
        [Authorize]
        public async Task<ActionResult<AnswerDto>> UpdateAnswer(int id, [FromBody] UpdateAnswerRequestDto updateAnswerRequest)
        {
            try
            {
                var currentUserId = GetCurrentUserId();
                if (!currentUserId.HasValue)
                {
                    return Unauthorized();
                }

                var answer = await _context.Answers
                    .Include(a => a.User)
                    .Include(a => a.Votes)
                    .FirstOrDefaultAsync(a => a.Id == id);

                if (answer == null)
                {
                    return NotFound(new { message = "Answer not found" });
                }

                if (answer.UserId != currentUserId.Value)
                {
                    return Forbid("You can only edit your own answers");
                }

                answer.Body = updateAnswerRequest.Body;
                answer.UpdatedAt = DateTime.UtcNow;

                await _context.SaveChangesAsync();

                var answerDto = _mapper.Map<AnswerDto>(answer);

                // Set user vote information if user is authenticated
                var userVote = answer.Votes.FirstOrDefault(v => v.UserId == currentUserId.Value);
                answerDto.UserVote = userVote?.IsUpvote == true ? "up" : 
                                   userVote?.IsUpvote == false ? "down" : null;

                return Ok(answerDto);
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { message = "An error occurred while updating the answer", details = ex.Message });
            }
        }

        [HttpDelete("answers/{id}")]
        [Authorize]
        public async Task<ActionResult> DeleteAnswer(int id)
        {
            try
            {
                var currentUserId = GetCurrentUserId();
                if (!currentUserId.HasValue)
                {
                    return Unauthorized();
                }

                var answer = await _context.Answers.FindAsync(id);
                if (answer == null)
                {
                    return NotFound(new { message = "Answer not found" });
                }

                if (answer.UserId != currentUserId.Value)
                {
                    return Forbid("You can only delete your own answers");
                }

                _context.Answers.Remove(answer);
                await _context.SaveChangesAsync();

                return NoContent();
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { message = "An error occurred while deleting the answer", details = ex.Message });
            }
        }

        private int? GetCurrentUserId()
        {
            var userIdClaim = User.FindFirst(ClaimTypes.NameIdentifier);
            return userIdClaim != null ? int.Parse(userIdClaim.Value) : null;
        }
    }
}
