using AutoMapper;
using WebApplication1.DTOs;
using WebApplication1.Models;

namespace WebApplication1.Mappings
{
    public class MappingProfile : Profile
    {
        public MappingProfile()
        {
            // User mappings
            CreateMap<User, UserDto>();

            // Tag mappings
            CreateMap<Tag, TagDto>();

            // Question mappings
            CreateMap<Question, QuestionDto>()
                .ForMember(dest => dest.Tags, opt => opt.MapFrom(src => src.QuestionTags.Select(qt => qt.Tag)))
                .ForMember(dest => dest.VoteCount, opt => opt.MapFrom(src => src.Votes.Sum(v => v.IsUpvote ? 1 : -1)))
                .ForMember(dest => dest.UserVote, opt => opt.Ignore()); // Will be set manually based on current user

            CreateMap<Question, QuestionSummaryDto>()
                .ForMember(dest => dest.Tags, opt => opt.MapFrom(src => src.QuestionTags.Select(qt => qt.Tag)))
                .ForMember(dest => dest.AnswerCount, opt => opt.MapFrom(src => src.Answers.Count))
                .ForMember(dest => dest.VoteCount, opt => opt.MapFrom(src => src.Votes.Sum(v => v.IsUpvote ? 1 : -1)))
                .ForMember(dest => dest.UserVote, opt => opt.Ignore()); // Will be set manually based on current user

            CreateMap<CreateQuestionRequestDto, Question>()
                .ForMember(dest => dest.Id, opt => opt.Ignore())
                .ForMember(dest => dest.CreatedAt, opt => opt.MapFrom(src => DateTime.UtcNow))
                .ForMember(dest => dest.UpdatedAt, opt => opt.Ignore())
                .ForMember(dest => dest.UserId, opt => opt.Ignore())
                .ForMember(dest => dest.User, opt => opt.Ignore())
                .ForMember(dest => dest.Answers, opt => opt.Ignore())
                .ForMember(dest => dest.Votes, opt => opt.Ignore())
                .ForMember(dest => dest.QuestionTags, opt => opt.Ignore())
                .ForSourceMember(src => src.Tags, opt => opt.DoNotValidate());

            // Similar question mapping for Gemini service
            CreateMap<Question, SimilarQuestionDto>()
                .ForMember(dest => dest.Tags, opt => opt.MapFrom(src => src.QuestionTags.Select(qt => qt.Tag)))
                .ForMember(dest => dest.AnswerCount, opt => opt.MapFrom(src => src.Answers.Count))
                .ForMember(dest => dest.VoteCount, opt => opt.MapFrom(src => src.Votes.Sum(v => v.IsUpvote ? 1 : -1)))
                .ForMember(dest => dest.SimilarityScore, opt => opt.Ignore()); // Will be set manually

            // Answer mappings
            CreateMap<Answer, AnswerDto>()
                .ForMember(dest => dest.VoteCount, opt => opt.MapFrom(src => src.Votes.Sum(v => v.IsUpvote ? 1 : -1)))
                .ForMember(dest => dest.UserVote, opt => opt.Ignore()); // Will be set manually based on current user

            CreateMap<CreateAnswerRequestDto, Answer>()
                .ForMember(dest => dest.Id, opt => opt.Ignore())
                .ForMember(dest => dest.CreatedAt, opt => opt.MapFrom(src => DateTime.UtcNow))
                .ForMember(dest => dest.UpdatedAt, opt => opt.Ignore())
                .ForMember(dest => dest.UserId, opt => opt.Ignore())
                .ForMember(dest => dest.User, opt => opt.Ignore())
                .ForMember(dest => dest.Question, opt => opt.Ignore())
                .ForMember(dest => dest.Votes, opt => opt.Ignore());

            // Vote mappings
            CreateMap<VoteRequestDto, Vote>()
                .ForMember(dest => dest.Id, opt => opt.Ignore())
                .ForMember(dest => dest.UserId, opt => opt.Ignore())
                .ForMember(dest => dest.CreatedAt, opt => opt.MapFrom(src => DateTime.UtcNow))
                .ForMember(dest => dest.User, opt => opt.Ignore())
                .ForMember(dest => dest.Question, opt => opt.Ignore())
                .ForMember(dest => dest.Answer, opt => opt.Ignore());
        }
    }
}
