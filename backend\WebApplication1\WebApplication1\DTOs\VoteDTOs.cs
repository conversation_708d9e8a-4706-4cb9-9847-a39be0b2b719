using System.ComponentModel.DataAnnotations;

namespace WebApplication1.DTOs
{
    public class VoteRequestDto
    {
        public int? QuestionId { get; set; }
        public int? AnswerId { get; set; }

        [Required]
        public bool IsUpvote { get; set; }
    }

    public class VoteResponseDto
    {
        public int VoteCount { get; set; }
        public string? UserVote { get; set; } // "up", "down", or null
    }
}
