import {
  Question,
  Answer,
  Tag,
  User,
  Vote,
  CreateQuestionRequest,
  CreateAnswerRequest,
  UpdateAnswerRequest,
  LoginRequest,
  RegisterRequest,
  AuthResponse,
  QuestionFilters,
  PaginatedResponse,
  VoteRequest,
} from '../types';
import {
  mockQuestions,
  mockAnswers,
  mockTags,
  mockUsers,
  mockVotes,
  getUserById,
  getTagByName,
  calculateVoteCount,
  getUserVote,
} from '../data/mockData';

// Simulate API delay
const delay = (ms: number = 500) => new Promise(resolve => setTimeout(resolve, ms));

// Current user simulation (you can change this to test different users)
let currentUserId: string | null = null;

class MockApiService {
  // Auth methods
  async login(credentials: LoginRequest): Promise<AuthResponse> {
    await delay(800);
    
    // Simple mock authentication - accept any email/password
    const user = mockUsers.find(u => u.email === credentials.email);
    if (!user) {
      throw new Error('User not found');
    }
    
    currentUserId = user.id;
    
    return {
      user,
      token: `mock-jwt-token-${user.id}`,
    };
  }

  async register(userData: RegisterRequest): Promise<AuthResponse> {
    await delay(1000);
    
    // Check if user already exists
    const existingUser = mockUsers.find(u => u.email === userData.email);
    if (existingUser) {
      throw new Error('User already exists');
    }
    
    // Create new user
    const newUser: User = {
      id: `${mockUsers.length + 1}`,
      username: userData.username,
      email: userData.email,
      createdAt: new Date().toISOString(),
    };
    
    mockUsers.push(newUser);
    currentUserId = newUser.id;
    
    return {
      user: newUser,
      token: `mock-jwt-token-${newUser.id}`,
    };
  }

  // Question methods
  async getQuestions(filters?: QuestionFilters): Promise<PaginatedResponse<Question>> {
    await delay();
    
    let filteredQuestions = [...mockQuestions];
    
    // Apply search filter
    if (filters?.search) {
      const searchTerm = filters.search.toLowerCase();
      filteredQuestions = filteredQuestions.filter(q =>
        q.title.toLowerCase().includes(searchTerm) ||
        q.body.toLowerCase().includes(searchTerm)
      );
    }
    
    // Apply tag filter
    if (filters?.tags && filters.tags.length > 0) {
      filteredQuestions = filteredQuestions.filter(q =>
        q.tags.some(tag => filters.tags!.includes(tag.name))
      );
    }
    
    // Apply sorting
    if (filters?.sortBy) {
      switch (filters.sortBy) {
        case 'newest':
          filteredQuestions.sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime());
          break;
        case 'votes':
          filteredQuestions.sort((a, b) => b.voteCount - a.voteCount);
          break;
        case 'activity':
          // Sort by latest answer or question creation
          filteredQuestions.sort((a, b) => {
            const aLatest = Math.max(
              new Date(a.createdAt).getTime(),
              ...a.answers.map(ans => new Date(ans.createdAt).getTime())
            );
            const bLatest = Math.max(
              new Date(b.createdAt).getTime(),
              ...b.answers.map(ans => new Date(ans.createdAt).getTime())
            );
            return bLatest - aLatest;
          });
          break;
      }
    }
    
    // Add user vote information if user is logged in
    if (currentUserId) {
      filteredQuestions = filteredQuestions.map(q => ({
        ...q,
        userVote: getUserVote(q.votes, currentUserId!),
        answers: q.answers.map(a => ({
          ...a,
          userVote: getUserVote(a.votes, currentUserId!),
        })),
      }));
    }
    
    // Apply pagination
    const page = filters?.page || 1;
    const limit = filters?.limit || 10;
    const startIndex = (page - 1) * limit;
    const endIndex = startIndex + limit;
    const paginatedQuestions = filteredQuestions.slice(startIndex, endIndex);
    
    return {
      data: paginatedQuestions,
      total: filteredQuestions.length,
      page,
      limit,
      totalPages: Math.ceil(filteredQuestions.length / limit),
    };
  }

  async getQuestionById(id: string): Promise<Question> {
    await delay();
    
    const question = mockQuestions.find(q => q.id === id);
    if (!question) {
      throw new Error('Question not found');
    }
    
    // Add user vote information if user is logged in
    let questionWithVotes = { ...question };
    if (currentUserId) {
      questionWithVotes = {
        ...question,
        userVote: getUserVote(question.votes, currentUserId),
        answers: question.answers.map(a => ({
          ...a,
          userVote: getUserVote(a.votes, currentUserId),
        })),
      };
    }
    
    return questionWithVotes;
  }

  async createQuestion(questionData: CreateQuestionRequest): Promise<Question> {
    await delay(1000);
    
    if (!currentUserId) {
      throw new Error('User not authenticated');
    }
    
    const user = getUserById(currentUserId);
    if (!user) {
      throw new Error('User not found');
    }
    
    // Find or create tags
    const tags = questionData.tags.map(tagName => {
      let tag = getTagByName(tagName);
      if (!tag) {
        tag = {
          id: `${mockTags.length + 1}`,
          name: tagName.toLowerCase(),
        };
        mockTags.push(tag);
      }
      return tag;
    });
    
    const newQuestion: Question = {
      id: `${mockQuestions.length + 1}`,
      title: questionData.title,
      body: questionData.body,
      createdAt: new Date().toISOString(),
      userId: currentUserId,
      user,
      tags,
      answers: [],
      votes: [],
      voteCount: 0,
      userVote: null,
    };
    
    mockQuestions.unshift(newQuestion); // Add to beginning
    return newQuestion;
  }

  // Answer methods
  async createAnswer(answerData: CreateAnswerRequest): Promise<Answer> {
    await delay(800);
    
    if (!currentUserId) {
      throw new Error('User not authenticated');
    }
    
    const user = getUserById(currentUserId);
    if (!user) {
      throw new Error('User not found');
    }
    
    const newAnswer: Answer = {
      id: `${mockAnswers.length + 1}`,
      body: answerData.body,
      createdAt: new Date().toISOString(),
      userId: currentUserId,
      user,
      questionId: answerData.questionId,
      votes: [],
      voteCount: 0,
      userVote: null,
    };
    
    mockAnswers.push(newAnswer);
    
    // Add answer to question
    const question = mockQuestions.find(q => q.id === answerData.questionId);
    if (question) {
      question.answers.push(newAnswer);
    }
    
    return newAnswer;
  }

  async updateAnswer(id: string, answerData: UpdateAnswerRequest): Promise<Answer> {
    await delay(600);
    
    const answer = mockAnswers.find(a => a.id === id);
    if (!answer) {
      throw new Error('Answer not found');
    }
    
    if (answer.userId !== currentUserId) {
      throw new Error('Not authorized to update this answer');
    }
    
    answer.body = answerData.body;
    answer.updatedAt = new Date().toISOString();
    
    // Update answer in question as well
    const question = mockQuestions.find(q => q.id === answer.questionId);
    if (question) {
      const answerIndex = question.answers.findIndex(a => a.id === id);
      if (answerIndex !== -1) {
        question.answers[answerIndex] = answer;
      }
    }
    
    return answer;
  }

  async deleteAnswer(id: string): Promise<void> {
    await delay(400);
    
    const answerIndex = mockAnswers.findIndex(a => a.id === id);
    if (answerIndex === -1) {
      throw new Error('Answer not found');
    }
    
    const answer = mockAnswers[answerIndex];
    if (answer.userId !== currentUserId) {
      throw new Error('Not authorized to delete this answer');
    }
    
    // Remove from mockAnswers
    mockAnswers.splice(answerIndex, 1);
    
    // Remove from question
    const question = mockQuestions.find(q => q.id === answer.questionId);
    if (question) {
      question.answers = question.answers.filter(a => a.id !== id);
    }
    
    // Remove associated votes
    const voteIndicesToRemove = mockVotes
      .map((vote, index) => vote.answerId === id ? index : -1)
      .filter(index => index !== -1)
      .reverse(); // Reverse to remove from end first
    
    voteIndicesToRemove.forEach(index => mockVotes.splice(index, 1));
  }

  // Vote methods
  async voteOnQuestion(questionId: string, isUpvote: boolean): Promise<{ voteCount: number; userVote: 'up' | 'down' | null }> {
    await delay(300);
    
    if (!currentUserId) {
      throw new Error('User not authenticated');
    }
    
    // Find existing vote
    const existingVoteIndex = mockVotes.findIndex(v => 
      v.questionId === questionId && v.userId === currentUserId
    );
    
    if (existingVoteIndex !== -1) {
      const existingVote = mockVotes[existingVoteIndex];
      if (existingVote.isUpvote === isUpvote) {
        // Remove vote if clicking same button
        mockVotes.splice(existingVoteIndex, 1);
      } else {
        // Change vote
        existingVote.isUpvote = isUpvote;
      }
    } else {
      // Add new vote
      const newVote: Vote = {
        id: `${mockVotes.length + 1}`,
        userId: currentUserId,
        questionId,
        isUpvote,
        createdAt: new Date().toISOString(),
      };
      mockVotes.push(newVote);
    }
    
    // Update question vote count
    const question = mockQuestions.find(q => q.id === questionId);
    if (question) {
      question.votes = mockVotes.filter(v => v.questionId === questionId);
      question.voteCount = calculateVoteCount(question.votes);
    }
    
    const questionVotes = mockVotes.filter(v => v.questionId === questionId);
    return {
      voteCount: calculateVoteCount(questionVotes),
      userVote: getUserVote(questionVotes, currentUserId),
    };
  }

  async voteOnAnswer(answerId: string, isUpvote: boolean): Promise<{ voteCount: number; userVote: 'up' | 'down' | null }> {
    await delay(300);
    
    if (!currentUserId) {
      throw new Error('User not authenticated');
    }
    
    // Find existing vote
    const existingVoteIndex = mockVotes.findIndex(v => 
      v.answerId === answerId && v.userId === currentUserId
    );
    
    if (existingVoteIndex !== -1) {
      const existingVote = mockVotes[existingVoteIndex];
      if (existingVote.isUpvote === isUpvote) {
        // Remove vote if clicking same button
        mockVotes.splice(existingVoteIndex, 1);
      } else {
        // Change vote
        existingVote.isUpvote = isUpvote;
      }
    } else {
      // Add new vote
      const newVote: Vote = {
        id: `${mockVotes.length + 1}`,
        userId: currentUserId,
        answerId,
        isUpvote,
        createdAt: new Date().toISOString(),
      };
      mockVotes.push(newVote);
    }
    
    // Update answer vote count
    const answer = mockAnswers.find(a => a.id === answerId);
    if (answer) {
      answer.votes = mockVotes.filter(v => v.answerId === answerId);
      answer.voteCount = calculateVoteCount(answer.votes);
      
      // Update answer in question as well
      const question = mockQuestions.find(q => q.id === answer.questionId);
      if (question) {
        const answerIndex = question.answers.findIndex(a => a.id === answerId);
        if (answerIndex !== -1) {
          question.answers[answerIndex] = answer;
        }
      }
    }
    
    const answerVotes = mockVotes.filter(v => v.answerId === answerId);
    return {
      voteCount: calculateVoteCount(answerVotes),
      userVote: getUserVote(answerVotes, currentUserId),
    };
  }

  // Tag methods
  async getAllTags(): Promise<Tag[]> {
    await delay(200);
    return [...mockTags];
  }

  async searchTags(query: string): Promise<Tag[]> {
    await delay(200);
    return mockTags.filter(tag => 
      tag.name.toLowerCase().includes(query.toLowerCase())
    );
  }

  // Helper method to set current user (for testing)
  setCurrentUser(userId: string | null) {
    currentUserId = userId;
  }

  getCurrentUserId(): string | null {
    return currentUserId;
  }
}

export const mockApiService = new MockApiService();
