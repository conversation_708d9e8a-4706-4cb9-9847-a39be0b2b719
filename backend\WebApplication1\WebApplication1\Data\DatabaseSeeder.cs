using WebApplication1.Models;

namespace WebApplication1.Data
{
    public static class DatabaseSeeder
    {
        public static void SeedDatabase(ApplicationDbContext context)
        {
            // Check if data already exists
            if (context.Users.Any())
            {
                return; // Database has been seeded
            }

            // Create sample users
            var users = new List<User>
            {
                new User
                {
                    Username = "john_doe",
                    Email = "<EMAIL>",
                    PasswordHash = BCrypt.Net.BCrypt.HashPassword("password123"),
                    CreatedAt = DateTime.UtcNow.AddDays(-30),
                    Reputation = 1250
                },
                new User
                {
                    Username = "jane_smith",
                    Email = "<EMAIL>",
                    PasswordHash = BCrypt.Net.BCrypt.HashPassword("password123"),
                    CreatedAt = DateTime.UtcNow.AddDays(-25),
                    Reputation = 890
                },
                new User
                {
                    Username = "dev_master",
                    Email = "<EMAIL>",
                    PasswordHash = BCrypt.Net.BCrypt.HashPassword("password123"),
                    CreatedAt = DateTime.UtcNow.AddDays(-20),
                    Reputation = 2100
                },
                new User
                {
                    Username = "react_ninja",
                    Email = "<EMAIL>",
                    PasswordHash = BCrypt.Net.BCrypt.HashPassword("password123"),
                    CreatedAt = DateTime.UtcNow.AddDays(-15),
                    Reputation = 1650
                },
                new User
                {
                    Username = "code_wizard",
                    Email = "<EMAIL>",
                    PasswordHash = BCrypt.Net.BCrypt.HashPassword("password123"),
                    CreatedAt = DateTime.UtcNow.AddDays(-10),
                    Reputation = 750
                }
            };

            context.Users.AddRange(users);
            context.SaveChanges();

            // Create sample tags
            var tags = new List<Tag>
            {
                new Tag { Name = "javascript" },
                new Tag { Name = "react" },
                new Tag { Name = "typescript" },
                new Tag { Name = "nodejs" },
                new Tag { Name = "css" },
                new Tag { Name = "html" },
                new Tag { Name = "python" },
                new Tag { Name = "api" },
                new Tag { Name = "database" },
                new Tag { Name = "authentication" },
                new Tag { Name = "frontend" },
                new Tag { Name = "backend" },
                new Tag { Name = "hooks" },
                new Tag { Name = "state-management" },
                new Tag { Name = "routing" }
            };

            context.Tags.AddRange(tags);
            context.SaveChanges();

            // Create sample questions
            var questions = new List<Question>
            {
                new Question
                {
                    Title = "How to use React hooks effectively in functional components?",
                    Body = @"I'm new to React hooks and I'm trying to understand how to use them effectively in functional components.

I've been working with class components for a while, but I want to migrate to functional components using hooks. Specifically, I'm confused about:

1. When to use useState vs useReducer
2. How to handle side effects with useEffect
3. Best practices for custom hooks

Here's what I've tried so far:

```javascript
function MyComponent() {
  const [data, setData] = useState(null);

  // Is this the right way to fetch data?
  useEffect(() => {
    fetchData().then(setData);
  }, []);

  return <div>{data ? data.name : 'Loading...'}</div>;
}
```

Any guidance would be appreciated!",
                    UserId = users[0].Id,
                    CreatedAt = DateTime.UtcNow.AddDays(-5)
                },
                new Question
                {
                    Title = "Best practices for implementing authentication in React applications?",
                    Body = @"I'm building a React application and need to implement user authentication. I'm looking for best practices and recommendations.

My requirements:
- JWT token-based authentication
- Protected routes
- Automatic token refresh
- Secure storage of tokens

I've heard about different approaches like:
- Context API for state management
- localStorage vs httpOnly cookies
- Axios interceptors for token handling

What's the most secure and maintainable approach? Should I use a library like Auth0 or implement it myself?

Any code examples or architectural guidance would be helpful!",
                    UserId = users[1].Id,
                    CreatedAt = DateTime.UtcNow.AddDays(-4)
                },
                new Question
                {
                    Title = "Should I use TypeScript with React? What are the benefits?",
                    Body = @"I'm starting a new React project and considering whether to use TypeScript. I've heard mixed opinions about it.

Pros I've heard:
- Better error catching
- Improved IDE support
- Self-documenting code

Cons I've heard:
- Steeper learning curve
- More boilerplate code
- Slower development initially

For context, I'm comfortable with JavaScript but new to TypeScript. The project will be medium-sized (around 50-100 components) and will be maintained by a team of 3-4 developers.

Is it worth the investment? What's your experience with TypeScript in React projects?",
                    UserId = users[4].Id,
                    CreatedAt = DateTime.UtcNow.AddDays(-3)
                },
                new Question
                {
                    Title = "How to optimize React app performance for large datasets?",
                    Body = @"I have a React application that displays large datasets (10,000+ items) and it's becoming slow. The app includes:

- A data table with sorting and filtering
- Real-time updates via WebSocket
- Complex nested components

Performance issues I'm experiencing:
- Slow initial render
- Laggy scrolling
- Memory leaks with frequent updates

I've tried:
- React.memo for component memoization
- useMemo and useCallback hooks
- Virtual scrolling libraries

What other optimization techniques should I consider? Are there specific patterns for handling large datasets in React?",
                    UserId = users[2].Id,
                    CreatedAt = DateTime.UtcNow.AddDays(-2)
                },
                new Question
                {
                    Title = "React Router v6 - How to implement nested routes properly?",
                    Body = @"I'm upgrading from React Router v5 to v6 and struggling with the new nested routes syntax.

In v5, I had:
```javascript
<Route path=""/dashboard"" component={Dashboard}>
  <Route path=""/dashboard/profile"" component={Profile} />
  <Route path=""/dashboard/settings"" component={Settings} />
</Route>
```

How do I achieve the same structure in v6? I've read about the new `Outlet` component but I'm not sure how to implement it correctly.

Also, how do I handle:
- Protected nested routes
- Dynamic route parameters
- Default child routes

Any examples would be greatly appreciated!",
                    UserId = users[3].Id,
                    CreatedAt = DateTime.UtcNow.AddDays(-1)
                }
            };

            context.Questions.AddRange(questions);
            context.SaveChanges();

            // Create question-tag relationships
            var questionTags = new List<QuestionTag>
            {
                // Question 1 tags: react, hooks, javascript
                new QuestionTag { QuestionId = questions[0].Id, TagId = tags.First(t => t.Name == "react").Id },
                new QuestionTag { QuestionId = questions[0].Id, TagId = tags.First(t => t.Name == "hooks").Id },
                new QuestionTag { QuestionId = questions[0].Id, TagId = tags.First(t => t.Name == "javascript").Id },

                // Question 2 tags: react, authentication, api
                new QuestionTag { QuestionId = questions[1].Id, TagId = tags.First(t => t.Name == "react").Id },
                new QuestionTag { QuestionId = questions[1].Id, TagId = tags.First(t => t.Name == "authentication").Id },
                new QuestionTag { QuestionId = questions[1].Id, TagId = tags.First(t => t.Name == "api").Id },

                // Question 3 tags: typescript, react, frontend
                new QuestionTag { QuestionId = questions[2].Id, TagId = tags.First(t => t.Name == "typescript").Id },
                new QuestionTag { QuestionId = questions[2].Id, TagId = tags.First(t => t.Name == "react").Id },
                new QuestionTag { QuestionId = questions[2].Id, TagId = tags.First(t => t.Name == "frontend").Id },

                // Question 4 tags: react, javascript, state-management
                new QuestionTag { QuestionId = questions[3].Id, TagId = tags.First(t => t.Name == "react").Id },
                new QuestionTag { QuestionId = questions[3].Id, TagId = tags.First(t => t.Name == "javascript").Id },
                new QuestionTag { QuestionId = questions[3].Id, TagId = tags.First(t => t.Name == "state-management").Id },

                // Question 5 tags: react, routing, frontend
                new QuestionTag { QuestionId = questions[4].Id, TagId = tags.First(t => t.Name == "react").Id },
                new QuestionTag { QuestionId = questions[4].Id, TagId = tags.First(t => t.Name == "routing").Id },
                new QuestionTag { QuestionId = questions[4].Id, TagId = tags.First(t => t.Name == "frontend").Id }
            };

            context.QuestionTags.AddRange(questionTags);
            context.SaveChanges();

            // Create sample answers
            var answers = new List<Answer>
            {
                new Answer
                {
                    Body = @"You can use React hooks to manage state in functional components. Here's a simple example:

```javascript
import React, { useState, useEffect } from 'react';

function MyComponent() {
  const [count, setCount] = useState(0);

  useEffect(() => {
    document.title = `Count: ${count}`;
  }, [count]);

  return (
    <div>
      <p>You clicked {count} times</p>
      <button onClick={() => setCount(count + 1)}>
        Click me
      </button>
    </div>
  );
}
```

The `useState` hook allows you to add state to functional components, while `useEffect` lets you perform side effects.",
                    UserId = users[1].Id,
                    QuestionId = questions[0].Id,
                    CreatedAt = DateTime.UtcNow.AddDays(-4).AddHours(2)
                },
                new Answer
                {
                    Body = @"For authentication in React, I recommend using JWT tokens with a context provider. Here's a basic setup:

```javascript
// AuthContext.js
import React, { createContext, useContext, useState } from 'react';

const AuthContext = createContext();

export const useAuth = () => useContext(AuthContext);

export const AuthProvider = ({ children }) => {
  const [user, setUser] = useState(null);
  const [token, setToken] = useState(localStorage.getItem('token'));

  const login = async (credentials) => {
    // API call to login
    const response = await fetch('/api/login', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(credentials)
    });

    const data = await response.json();
    setUser(data.user);
    setToken(data.token);
    localStorage.setItem('token', data.token);
  };

  const logout = () => {
    setUser(null);
    setToken(null);
    localStorage.removeItem('token');
  };

  return (
    <AuthContext.Provider value={{ user, token, login, logout }}>
      {children}
    </AuthContext.Provider>
  );
};
```

This provides a clean way to manage authentication state across your app.",
                    UserId = users[2].Id,
                    QuestionId = questions[1].Id,
                    CreatedAt = DateTime.UtcNow.AddDays(-3).AddHours(5)
                },
                new Answer
                {
                    Body = @"TypeScript with React is amazing! Here are some key benefits:

1. **Type Safety**: Catch errors at compile time
2. **Better IntelliSense**: Improved autocomplete and refactoring
3. **Self-documenting code**: Types serve as documentation
4. **Easier refactoring**: Confident code changes

Here's a simple typed component:

```typescript
interface Props {
  title: string;
  count: number;
  onIncrement: () => void;
}

const Counter: React.FC<Props> = ({ title, count, onIncrement }) => {
  return (
    <div>
      <h2>{title}</h2>
      <p>Count: {count}</p>
      <button onClick={onIncrement}>Increment</button>
    </div>
  );
};
```

The learning curve is worth it for the long-term benefits!",
                    UserId = users[3].Id,
                    QuestionId = questions[2].Id,
                    CreatedAt = DateTime.UtcNow.AddDays(-2).AddHours(3)
                }
            };

            context.Answers.AddRange(answers);
            context.SaveChanges();

            // Create sample votes
            var votes = new List<Vote>
            {
                // Votes on questions
                new Vote { UserId = users[1].Id, QuestionId = questions[0].Id, IsUpvote = true, CreatedAt = DateTime.UtcNow.AddDays(-4) },
                new Vote { UserId = users[2].Id, QuestionId = questions[0].Id, IsUpvote = true, CreatedAt = DateTime.UtcNow.AddDays(-4) },
                new Vote { UserId = users[3].Id, QuestionId = questions[0].Id, IsUpvote = false, CreatedAt = DateTime.UtcNow.AddDays(-4) },

                new Vote { UserId = users[0].Id, QuestionId = questions[1].Id, IsUpvote = true, CreatedAt = DateTime.UtcNow.AddDays(-3) },
                new Vote { UserId = users[2].Id, QuestionId = questions[1].Id, IsUpvote = true, CreatedAt = DateTime.UtcNow.AddDays(-3) },

                new Vote { UserId = users[0].Id, QuestionId = questions[2].Id, IsUpvote = true, CreatedAt = DateTime.UtcNow.AddDays(-2) },

                // Votes on answers
                new Vote { UserId = users[0].Id, AnswerId = answers[0].Id, IsUpvote = true, CreatedAt = DateTime.UtcNow.AddDays(-4) },
                new Vote { UserId = users[3].Id, AnswerId = answers[0].Id, IsUpvote = true, CreatedAt = DateTime.UtcNow.AddDays(-4) },

                new Vote { UserId = users[0].Id, AnswerId = answers[1].Id, IsUpvote = false, CreatedAt = DateTime.UtcNow.AddDays(-3) },

                new Vote { UserId = users[0].Id, AnswerId = answers[2].Id, IsUpvote = true, CreatedAt = DateTime.UtcNow.AddDays(-2) },
                new Vote { UserId = users[1].Id, AnswerId = answers[2].Id, IsUpvote = true, CreatedAt = DateTime.UtcNow.AddDays(-2) }
            };

            context.Votes.AddRange(votes);
            context.SaveChanges();
        }
    }
}
