import { User, Question, Answer, Tag, Vote } from '../types';

// Mock Users
export const mockUsers: User[] = [
  {
    id: '1',
    username: 'john_doe',
    email: '<EMAIL>',
    createdAt: '2024-01-15T10:30:00Z',
  },
  {
    id: '2',
    username: 'jane_smith',
    email: '<EMAIL>',
    createdAt: '2024-01-10T14:20:00Z',
  },
  {
    id: '3',
    username: 'dev_master',
    email: '<EMAIL>',
    createdAt: '2024-01-05T09:15:00Z',
  },
  {
    id: '4',
    username: 'react_ninja',
    email: '<EMAIL>',
    createdAt: '2024-01-20T16:45:00Z',
  },
  {
    id: '5',
    username: 'code_wizard',
    email: '<EMAIL>',
    createdAt: '2024-01-12T11:30:00Z',
  },
];

// Mock Tags
export const mockTags: Tag[] = [
  { id: '1', name: 'javascript' },
  { id: '2', name: 'react' },
  { id: '3', name: 'typescript' },
  { id: '4', name: 'nodejs' },
  { id: '5', name: 'css' },
  { id: '6', name: 'html' },
  { id: '7', name: 'python' },
  { id: '8', name: 'api' },
  { id: '9', name: 'database' },
  { id: '10', name: 'authentication' },
  { id: '11', name: 'frontend' },
  { id: '12', name: 'backend' },
  { id: '13', name: 'hooks' },
  { id: '14', name: 'state-management' },
  { id: '15', name: 'routing' },
];

// Mock Votes
export const mockVotes: Vote[] = [
  { id: '1', userId: '2', questionId: '1', isUpvote: true, createdAt: '2024-01-25T10:00:00Z' },
  { id: '2', userId: '3', questionId: '1', isUpvote: true, createdAt: '2024-01-25T11:00:00Z' },
  { id: '3', userId: '4', questionId: '1', isUpvote: false, createdAt: '2024-01-25T12:00:00Z' },
  { id: '4', userId: '1', questionId: '2', isUpvote: true, createdAt: '2024-01-24T15:00:00Z' },
  { id: '5', userId: '3', questionId: '2', isUpvote: true, createdAt: '2024-01-24T16:00:00Z' },
  { id: '6', userId: '2', answerId: '1', isUpvote: true, createdAt: '2024-01-25T13:00:00Z' },
  { id: '7', userId: '4', answerId: '1', isUpvote: true, createdAt: '2024-01-25T14:00:00Z' },
  { id: '8', userId: '1', answerId: '2', isUpvote: false, createdAt: '2024-01-24T17:00:00Z' },
];

// Mock Answers
export const mockAnswers: Answer[] = [
  {
    id: '1',
    body: `You can use React hooks to manage state in functional components. Here's a simple example:

\`\`\`javascript
import React, { useState, useEffect } from 'react';

function MyComponent() {
  const [count, setCount] = useState(0);
  
  useEffect(() => {
    document.title = \`Count: \${count}\`;
  }, [count]);
  
  return (
    <div>
      <p>You clicked {count} times</p>
      <button onClick={() => setCount(count + 1)}>
        Click me
      </button>
    </div>
  );
}
\`\`\`

The \`useState\` hook allows you to add state to functional components, while \`useEffect\` lets you perform side effects.`,
    createdAt: '2024-01-25T12:30:00Z',
    userId: '2',
    user: mockUsers[1],
    questionId: '1',
    votes: mockVotes.filter(v => v.answerId === '1'),
    voteCount: 2,
    userVote: null,
  },
  {
    id: '2',
    body: `For authentication in React, I recommend using JWT tokens with a context provider. Here's a basic setup:

\`\`\`javascript
// AuthContext.js
import React, { createContext, useContext, useState } from 'react';

const AuthContext = createContext();

export const useAuth = () => useContext(AuthContext);

export const AuthProvider = ({ children }) => {
  const [user, setUser] = useState(null);
  const [token, setToken] = useState(localStorage.getItem('token'));
  
  const login = async (credentials) => {
    // API call to login
    const response = await fetch('/api/login', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(credentials)
    });
    
    const data = await response.json();
    setUser(data.user);
    setToken(data.token);
    localStorage.setItem('token', data.token);
  };
  
  const logout = () => {
    setUser(null);
    setToken(null);
    localStorage.removeItem('token');
  };
  
  return (
    <AuthContext.Provider value={{ user, token, login, logout }}>
      {children}
    </AuthContext.Provider>
  );
};
\`\`\`

This provides a clean way to manage authentication state across your app.`,
    createdAt: '2024-01-24T16:45:00Z',
    userId: '3',
    user: mockUsers[2],
    questionId: '2',
    votes: mockVotes.filter(v => v.answerId === '2'),
    voteCount: -1,
    userVote: null,
  },
  {
    id: '3',
    body: `TypeScript with React is amazing! Here are some key benefits:

1. **Type Safety**: Catch errors at compile time
2. **Better IntelliSense**: Improved autocomplete and refactoring
3. **Self-documenting code**: Types serve as documentation
4. **Easier refactoring**: Confident code changes

Here's a simple typed component:

\`\`\`typescript
interface Props {
  title: string;
  count: number;
  onIncrement: () => void;
}

const Counter: React.FC<Props> = ({ title, count, onIncrement }) => {
  return (
    <div>
      <h2>{title}</h2>
      <p>Count: {count}</p>
      <button onClick={onIncrement}>Increment</button>
    </div>
  );
};
\`\`\`

The learning curve is worth it for the long-term benefits!`,
    createdAt: '2024-01-23T14:20:00Z',
    userId: '4',
    user: mockUsers[3],
    questionId: '3',
    votes: [],
    voteCount: 0,
    userVote: null,
  },
];

// Mock Questions
export const mockQuestions: Question[] = [
  {
    id: '1',
    title: 'How to use React hooks effectively in functional components?',
    body: `I'm new to React hooks and I'm trying to understand how to use them effectively in functional components. 

I've been working with class components for a while, but I want to migrate to functional components using hooks. Specifically, I'm confused about:

1. When to use useState vs useReducer
2. How to handle side effects with useEffect
3. Best practices for custom hooks

Here's what I've tried so far:

\`\`\`javascript
function MyComponent() {
  const [data, setData] = useState(null);
  
  // Is this the right way to fetch data?
  useEffect(() => {
    fetchData().then(setData);
  }, []);
  
  return <div>{data ? data.name : 'Loading...'}</div>;
}
\`\`\`

Any guidance would be appreciated!`,
    createdAt: '2024-01-25T10:15:00Z',
    updatedAt: '2024-01-25T10:15:00Z',
    userId: '1',
    user: mockUsers[0],
    tags: [mockTags[1], mockTags[12], mockTags[0]], // react, hooks, javascript
    answers: [mockAnswers[0]],
    votes: mockVotes.filter(v => v.questionId === '1'),
    voteCount: 1,
    userVote: null,
  },
  {
    id: '2',
    title: 'Best practices for implementing authentication in React applications?',
    body: `I'm building a React application and need to implement user authentication. I'm looking for best practices and recommendations.

My requirements:
- JWT token-based authentication
- Protected routes
- Automatic token refresh
- Secure storage of tokens

I've heard about different approaches like:
- Context API for state management
- localStorage vs httpOnly cookies
- Axios interceptors for token handling

What's the most secure and maintainable approach? Should I use a library like Auth0 or implement it myself?

Any code examples or architectural guidance would be helpful!`,
    createdAt: '2024-01-24T15:30:00Z',
    userId: '2',
    user: mockUsers[1],
    tags: [mockTags[1], mockTags[9], mockTags[7]], // react, authentication, api
    answers: [mockAnswers[1]],
    votes: mockVotes.filter(v => v.questionId === '2'),
    voteCount: 2,
    userVote: null,
  },
  {
    id: '3',
    title: 'Should I use TypeScript with React? What are the benefits?',
    body: `I'm starting a new React project and considering whether to use TypeScript. I've heard mixed opinions about it.

Pros I've heard:
- Better error catching
- Improved IDE support
- Self-documenting code

Cons I've heard:
- Steeper learning curve
- More boilerplate code
- Slower development initially

For context, I'm comfortable with JavaScript but new to TypeScript. The project will be medium-sized (around 50-100 components) and will be maintained by a team of 3-4 developers.

Is it worth the investment? What's your experience with TypeScript in React projects?`,
    createdAt: '2024-01-23T13:45:00Z',
    userId: '5',
    user: mockUsers[4],
    tags: [mockTags[2], mockTags[1], mockTags[10]], // typescript, react, frontend
    answers: [mockAnswers[2]],
    votes: [],
    voteCount: 0,
    userVote: null,
  },
  {
    id: '4',
    title: 'How to optimize React app performance for large datasets?',
    body: `I have a React application that displays large datasets (10,000+ items) and it's becoming slow. The app includes:

- A data table with sorting and filtering
- Real-time updates via WebSocket
- Complex nested components

Performance issues I'm experiencing:
- Slow initial render
- Laggy scrolling
- Memory leaks with frequent updates

I've tried:
- React.memo for component memoization
- useMemo and useCallback hooks
- Virtual scrolling libraries

What other optimization techniques should I consider? Are there specific patterns for handling large datasets in React?`,
    createdAt: '2024-01-22T11:20:00Z',
    userId: '3',
    user: mockUsers[2],
    tags: [mockTags[1], mockTags[0], mockTags[13]], // react, javascript, state-management
    answers: [],
    votes: [],
    voteCount: 0,
    userVote: null,
  },
  {
    id: '5',
    title: 'React Router v6 - How to implement nested routes properly?',
    body: `I'm upgrading from React Router v5 to v6 and struggling with the new nested routes syntax.

In v5, I had:
\`\`\`javascript
<Route path="/dashboard" component={Dashboard}>
  <Route path="/dashboard/profile" component={Profile} />
  <Route path="/dashboard/settings" component={Settings} />
</Route>
\`\`\`

How do I achieve the same structure in v6? I've read about the new \`Outlet\` component but I'm not sure how to implement it correctly.

Also, how do I handle:
- Protected nested routes
- Dynamic route parameters
- Default child routes

Any examples would be greatly appreciated!`,
    createdAt: '2024-01-21T16:10:00Z',
    userId: '4',
    user: mockUsers[3],
    tags: [mockTags[1], mockTags[14], mockTags[10]], // react, routing, frontend
    answers: [],
    votes: [],
    voteCount: 0,
    userVote: null,
  },
];

// Helper function to get user by ID
export const getUserById = (id: string): User | undefined => {
  return mockUsers.find(user => user.id === id);
};

// Helper function to get tag by name
export const getTagByName = (name: string): Tag | undefined => {
  return mockTags.find(tag => tag.name.toLowerCase() === name.toLowerCase());
};

// Helper function to calculate vote count for a question or answer
export const calculateVoteCount = (votes: Vote[]): number => {
  return votes.reduce((count, vote) => {
    return vote.isUpvote ? count + 1 : count - 1;
  }, 0);
};

// Helper function to get user's vote on a question or answer
export const getUserVote = (votes: Vote[], userId: string): 'up' | 'down' | null => {
  const userVote = votes.find(vote => vote.userId === userId);
  if (!userVote) return null;
  return userVote.isUpvote ? 'up' : 'down';
};
