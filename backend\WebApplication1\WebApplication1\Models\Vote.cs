using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace WebApplication1.Models
{
    public class Vote
    {
        [Key]
        public int Id { get; set; }

        [Required]
        public int UserId { get; set; }

        public int? QuestionId { get; set; }
        public int? AnswerId { get; set; }

        [Required]
        public bool IsUpvote { get; set; }

        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

        // Navigation properties
        [ForeignKey("UserId")]
        public virtual User User { get; set; } = null!;

        [ForeignKey("QuestionId")]
        public virtual Question? Question { get; set; }

        [ForeignKey("AnswerId")]
        public virtual Answer? Answer { get; set; }
    }
}
