import React, { useState, useEffect } from "react";
import { useSearchParams, useNavigate } from "react-router-dom";
import { motion } from "framer-motion";
import { Question, QuestionSummary, QuestionFilters } from "../types";
import { questionService } from "../services/questionService";
import QuestionCard from "../components/QuestionCard";

const Home: React.FC = () => {
  const [searchParams] = useSearchParams();
  const navigate = useNavigate();
  const [questions, setQuestions] = useState<(Question | QuestionSummary)[]>(
    []
  );
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [sortBy, setSortBy] = useState<"newest" | "votes" | "activity">(
    "newest"
  );
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);

  const searchQuery = searchParams.get("search") || "";

  useEffect(() => {
    loadQuestions();
  }, [searchQuery, sortBy, currentPage]);

  const loadQuestions = async () => {
    setIsLoading(true);
    setError(null);

    try {
      const filters: QuestionFilters = {
        sortBy,
        page: currentPage,
        limit: 10,
      };

      if (searchQuery) {
        filters.search = searchQuery;
      }

      const response = await questionService.getQuestions(filters);
      setQuestions(response.data);
      setTotalPages(response.totalPages);
    } catch (err) {
      setError("Failed to load questions. Please try again.");
      console.error("Error loading questions:", err);
    } finally {
      setIsLoading(false);
    }
  };

  const handleVoteUpdate = (
    questionId: number,
    newVoteCount: number,
    userVote: "up" | "down" | null
  ) => {
    setQuestions((prev) =>
      prev.map((q) =>
        q.id === questionId ? { ...q, voteCount: newVoteCount, userVote } : q
      )
    );
  };

  const handleSortChange = (newSortBy: "newest" | "votes" | "activity") => {
    setSortBy(newSortBy);
    setCurrentPage(1);
  };

  if (isLoading) {
    return (
      <div className="flex justify-center items-center min-h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-orange-500"></div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="text-center py-12">
        <div className="text-red-600 text-lg">{error}</div>
        <button
          onClick={loadQuestions}
          className="mt-4 px-4 py-2 bg-orange-500 text-white rounded-lg hover:bg-orange-600 transition-colors duration-200"
        >
          Try Again
        </button>
      </div>
    );
  }

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      exit={{ opacity: 0, y: -20 }}
      transition={{ duration: 0.3 }}
      className="space-y-6"
    >
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">
            {searchQuery
              ? `Search Results for "${searchQuery}"`
              : "All Questions"}
          </h1>
          <p className="text-gray-600 mt-1">
            {questions.length} question{questions.length !== 1 ? "s" : ""}
          </p>
        </div>

        <button
          onClick={() => navigate("/ask")}
          className="bg-orange-500 hover:bg-orange-600 text-white px-6 py-3 rounded-lg font-medium transition-colors duration-200"
        >
          Ask Question
        </button>
      </div>

      {/* Sort Options */}
      <div className="flex space-x-4 border-b border-gray-200">
        {[
          { key: "newest", label: "Newest" },
          { key: "votes", label: "Most Votes" },
          { key: "activity", label: "Recent Activity" },
        ].map(({ key, label }) => (
          <button
            key={key}
            onClick={() => handleSortChange(key as any)}
            className={`pb-2 px-1 border-b-2 font-medium text-sm transition-colors duration-200 ${
              sortBy === key
                ? "border-orange-500 text-orange-600"
                : "border-transparent text-gray-500 hover:text-gray-700"
            }`}
          >
            {label}
          </button>
        ))}
      </div>

      {/* Questions List */}
      {questions.length === 0 ? (
        <div className="text-center py-12">
          <div className="text-gray-500 text-lg">
            {searchQuery
              ? "No questions found matching your search."
              : "No questions yet."}
          </div>
          {!searchQuery && (
            <button
              onClick={() => navigate("/ask")}
              className="mt-4 px-6 py-3 bg-orange-500 text-white rounded-lg hover:bg-orange-600 transition-colors duration-200"
            >
              Ask the First Question
            </button>
          )}
        </div>
      ) : (
        <div className="space-y-4">
          {questions.map((question) => (
            <QuestionCard
              key={question.id}
              question={question}
              onVoteUpdate={handleVoteUpdate}
            />
          ))}
        </div>
      )}

      {/* Pagination */}
      {totalPages > 1 && (
        <div className="flex justify-center space-x-2 mt-8">
          <button
            onClick={() => setCurrentPage((prev) => Math.max(1, prev - 1))}
            disabled={currentPage === 1}
            className="px-4 py-2 border border-gray-300 rounded-lg disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-50 transition-colors duration-200"
          >
            Previous
          </button>

          <span className="px-4 py-2 text-gray-700">
            Page {currentPage} of {totalPages}
          </span>

          <button
            onClick={() =>
              setCurrentPage((prev) => Math.min(totalPages, prev + 1))
            }
            disabled={currentPage === totalPages}
            className="px-4 py-2 border border-gray-300 rounded-lg disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-50 transition-colors duration-200"
          >
            Next
          </button>
        </div>
      )}
    </motion.div>
  );
};

export default Home;
