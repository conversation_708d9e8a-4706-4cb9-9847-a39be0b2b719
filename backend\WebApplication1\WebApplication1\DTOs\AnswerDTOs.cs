using System.ComponentModel.DataAnnotations;

namespace WebApplication1.DTOs
{
    public class CreateAnswerRequestDto
    {
        [Required]
        [MinLength(30)]
        public string Body { get; set; } = string.Empty;

        [Required]
        public int QuestionId { get; set; }
    }

    public class UpdateAnswerRequestDto
    {
        [Required]
        [MinLength(30)]
        public string Body { get; set; } = string.Empty;
    }

    public class AnswerDto
    {
        public int Id { get; set; }
        public string Body { get; set; } = string.Empty;
        public DateTime CreatedAt { get; set; }
        public DateTime? UpdatedAt { get; set; }
        public int UserId { get; set; }
        public UserDto User { get; set; } = null!;
        public int QuestionId { get; set; }
        public int VoteCount { get; set; }
        public string? UserVote { get; set; } // "up", "down", or null
    }
}
