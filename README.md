# Stack Overflow Clone - Full Stack Application

A comprehensive Stack Overflow clone built with React TypeScript frontend and ASP.NET Core backend.

## 🚀 Features

### ✅ Complete Implementation
- **JWT Authentication** - Secure user registration and login
- **Question Management** - Create, view, search, and filter questions
- **Answer System** - Add, edit, and delete answers with ownership validation
- **Voting System** - Real-time upvote/downvote on questions and answers
- **Tag Management** - Create and search tags with autocomplete
- **Search & Filtering** - Advanced search with sorting options
- **Responsive Design** - Mobile-friendly interface
- **Real-time Updates** - Live vote counts and content updates
- **Database Integration** - Full SQL Server integration with Entity Framework

### 🎨 Tech Stack

#### Frontend
- **React 19** with TypeScript
- **Tailwind CSS** for styling
- **Framer Motion** for animations
- **React Router** for navigation
- **Vite** for build tooling

#### Backend
- **ASP.NET Core 8** - Web API framework
- **Entity Framework Core** - ORM with SQL Server
- **JWT Authentication** - Secure token-based auth
- **AutoMapper** - Object mapping
- **Swagger/OpenAPI** - API documentation

## 🏃‍♂️ Quick Start

### Prerequisites
- **Node.js** (v16 or higher)
- **.NET 8 SDK**
- **SQL Server** or **SQL Server Express LocalDB**
- **Visual Studio 2022** or **VS Code** (recommended)

### 1. Clone the Repository
```bash
git clone <your-repo-url>
cd stack-overflow-clone
```

### 2. Setup Backend (.NET API)
```bash
# Navigate to backend
cd backend/WebApplication1/WebApplication1

# Restore packages
dotnet restore

# Run the API (will create database automatically)
dotnet run
```

The backend will start at `https://localhost:7071` with Swagger UI available at the root URL.

### 3. Setup Frontend (React)
```bash
# Navigate to frontend (in a new terminal)
cd frontend

# Install dependencies
npm install

# Start development server
npm run dev
```

The frontend will start at `http://localhost:5173`.

### 4. Access the Application
- **Frontend**: http://localhost:5173
- **Backend API**: https://localhost:7071
- **API Documentation**: https://localhost:7071 (Swagger UI)

## 📊 Sample Data & Testing

### Pre-loaded Sample Users
All users have password: `password123`

- **john_doe** (<EMAIL>)
- **jane_smith** (<EMAIL>)
- **dev_master** (<EMAIL>)
- **react_ninja** (<EMAIL>)
- **code_wizard** (<EMAIL>)

### Sample Content
- **5 Questions** with detailed content and code examples
- **3 Answers** with comprehensive explanations
- **15 Tags** covering popular technologies
- **Sample Votes** to demonstrate the voting system

### Testing Workflow
1. **Login**: Use any sample user email with password `password123`
2. **Browse**: Explore questions with search and filtering
3. **Vote**: Upvote/downvote questions and answers
4. **Ask**: Create new questions with tags
5. **Answer**: Add answers to existing questions
6. **Search**: Try searching for "React", "authentication", etc.

## 🔧 Configuration

### Backend Configuration
Update `backend/WebApplication1/WebApplication1/appsettings.json`:

```json
{
  "ConnectionStrings": {
    "DefaultConnection": "Server=(localdb)\\mssqllocaldb;Database=StackOverflowCloneDB;Trusted_Connection=true;MultipleActiveResultSets=true"
  },
  "JwtSettings": {
    "SecretKey": "YourSuperSecretKeyThatIsAtLeast32CharactersLong!@#$%^&*()",
    "Issuer": "StackOverflowCloneAPI",
    "Audience": "StackOverflowCloneClient",
    "ExpiryInHours": "24"
  }
}
```

### Frontend Configuration
Create `frontend/.env` (optional):

```env
VITE_API_URL=https://localhost:7071/api
VITE_NODE_ENV=development
```

## 🗄️ Database

### Automatic Setup
The application automatically:
- Creates the database on first run
- Sets up all tables and relationships
- Seeds sample data

### Manual Database Management
If you prefer using migrations:

```bash
# In backend directory
dotnet ef migrations add InitialCreate
dotnet ef database update
```

### Database Schema
- **Users** - User accounts and authentication
- **Questions** - Question posts with metadata
- **Answers** - Answer posts linked to questions
- **Tags** - Tag system for categorization
- **Votes** - Voting system for questions and answers
- **QuestionTags** - Many-to-many relationship

## 🔒 Security Features

- **Password Hashing** - BCrypt for secure password storage
- **JWT Tokens** - Stateless authentication with configurable expiry
- **CORS Protection** - Configured for frontend integration
- **Input Validation** - Comprehensive validation on all endpoints
- **Authorization** - Protected endpoints require valid authentication
- **SQL Injection Protection** - Entity Framework parameterized queries

## 📁 Project Structure

```
stack-overflow-clone/
├── frontend/                 # React TypeScript frontend
│   ├── src/
│   │   ├── components/      # Reusable UI components
│   │   ├── pages/          # Page components
│   │   ├── services/       # API service layer
│   │   ├── contexts/       # React contexts
│   │   ├── types/          # TypeScript types
│   │   └── data/           # Mock data (fallback)
│   ├── public/
│   └── package.json
├── backend/                  # ASP.NET Core backend
│   └── WebApplication1/
│       └── WebApplication1/
│           ├── Controllers/ # API controllers
│           ├── Models/      # Entity models
│           ├── DTOs/        # Data transfer objects
│           ├── Data/        # Database context
│           ├── Services/    # Business logic
│           └── Mappings/    # AutoMapper profiles
└── README.md
```

## 🚀 Deployment

### Development
Both frontend and backend run locally with hot reload enabled.

### Production
1. **Backend**: Deploy to Azure App Service or IIS
2. **Frontend**: Build and deploy to Netlify, Vercel, or Azure Static Web Apps
3. **Database**: Use Azure SQL Database or SQL Server

### Environment Variables
- Update API URLs for production
- Use secure JWT secrets
- Configure production database connections
- Set up proper CORS origins

## 🔮 Future Enhancements

- User reputation and badge system
- Question categories and advanced filtering
- Rich text editor with markdown support
- File upload for images and attachments
- Email notifications for answers and comments
- Comment system on answers
- Admin panel for content moderation
- Advanced search with Elasticsearch
- Real-time notifications with SignalR
- Rate limiting and API throttling

## 🐛 Troubleshooting

### Common Issues

1. **CORS Errors**: Ensure backend is running and CORS is configured
2. **Database Connection**: Check SQL Server is running and connection string is correct
3. **JWT Errors**: Verify JWT settings match between frontend and backend
4. **Port Conflicts**: Change ports in configuration if needed

### Getting Help

1. Check the console for error messages
2. Verify both frontend and backend are running
3. Test API endpoints directly using Swagger UI
4. Check database was created and seeded properly

## 📝 Notes

- Database is created automatically on first backend run
- Sample data is seeded automatically
- JWT tokens expire after 24 hours (configurable)
- All API endpoints are documented in Swagger UI
- Frontend automatically switches between mock and real API
- HTTPS is required for JWT authentication in production
