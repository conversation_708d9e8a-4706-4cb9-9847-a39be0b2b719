import { CreateQuestionRequest } from "../types";

const CACHE_KEY = "askQuestion_draft";
const CACHE_EXPIRY_HOURS = 24;

interface CachedQuestionData {
  data: CreateQuestionRequest;
  timestamp: number;
  expiresAt: number;
}

export const cacheService = {
  // Save current question draft to localStorage
  saveDraft(questionData: CreateQuestionRequest): void {
    try {
      const now = Date.now();
      const expiresAt = now + (CACHE_EXPIRY_HOURS * 60 * 60 * 1000);
      
      const cachedData: CachedQuestionData = {
        data: questionData,
        timestamp: now,
        expiresAt
      };
      
      localStorage.setItem(CACHE_KEY, JSON.stringify(cachedData));
    } catch (error) {
      console.error("Error saving draft to cache:", error);
    }
  },

  // Load question draft from localStorage
  loadDraft(): CreateQuestionRequest | null {
    try {
      const cached = localStorage.getItem(CACHE_KEY);
      if (!cached) return null;

      const cachedData: CachedQuestionData = JSON.parse(cached);
      
      // Check if cache has expired
      if (Date.now() > cachedData.expiresAt) {
        this.clearDraft();
        return null;
      }

      return cachedData.data;
    } catch (error) {
      console.error("Error loading draft from cache:", error);
      return null;
    }
  },

  // Clear the cached draft
  clearDraft(): void {
    try {
      localStorage.removeItem(CACHE_KEY);
    } catch (error) {
      console.error("Error clearing draft from cache:", error);
    }
  },

  // Check if there's a valid cached draft
  hasDraft(): boolean {
    const draft = this.loadDraft();
    return draft !== null && (
      draft.title.trim() !== "" || 
      draft.body.trim() !== "" || 
      draft.tags.length > 0
    );
  },

  // Auto-save functionality - debounced save
  autoSave: (() => {
    let timeoutId: NodeJS.Timeout | null = null;
    
    return (questionData: CreateQuestionRequest, delay: number = 2000) => {
      if (timeoutId) {
        clearTimeout(timeoutId);
      }
      
      timeoutId = setTimeout(() => {
        // Only save if there's meaningful content
        if (questionData.title.trim() || questionData.body.trim() || questionData.tags.length > 0) {
          cacheService.saveDraft(questionData);
        }
      }, delay);
    };
  })(),

  // Get cache info for display
  getCacheInfo(): { hasCache: boolean; lastSaved?: Date } | null {
    try {
      const cached = localStorage.getItem(CACHE_KEY);
      if (!cached) return { hasCache: false };

      const cachedData: CachedQuestionData = JSON.parse(cached);
      
      // Check if cache has expired
      if (Date.now() > cachedData.expiresAt) {
        return { hasCache: false };
      }

      return {
        hasCache: true,
        lastSaved: new Date(cachedData.timestamp)
      };
    } catch (error) {
      console.error("Error getting cache info:", error);
      return { hasCache: false };
    }
  }
};
