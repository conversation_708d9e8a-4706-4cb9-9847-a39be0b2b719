import React, { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import { motion } from "framer-motion";
import { useAuth } from "../contexts/AuthContext";
import {
  CreateQuestionRequest,
  FormErrors,
  Tag,
  SimilarQuestionsResponse,
} from "../types";
import { questionService } from "../services/questionService";
import { tagService } from "../services/tagService";
import { geminiService } from "../services/geminiService";
import { cacheService } from "../services/cacheService";
import SimilarQuestionsSuggestion from "../components/SimilarQuestionsSuggestion";

const AskQuestion: React.FC = () => {
  const navigate = useNavigate();
  const { isAuthenticated } = useAuth();

  const [formData, setFormData] = useState<CreateQuestionRequest>({
    title: "",
    body: "",
    tags: [],
  });
  const [errors, setErrors] = useState<FormErrors>({});
  const [submitError, setSubmitError] = useState<string>("");
  const [isLoading, setIsLoading] = useState(false);
  const [availableTags, setAvailableTags] = useState<Tag[]>([]);
  const [tagInput, setTagInput] = useState("");
  const [showTagSuggestions, setShowTagSuggestions] = useState(false);

  // Similarity checking states
  const [showSimilarQuestions, setShowSimilarQuestions] = useState(false);
  const [isCheckingSimilarity, setIsCheckingSimilarity] = useState(false);
  const [similarQuestionsData, setSimilarQuestionsData] =
    useState<SimilarQuestionsResponse>({
      hasSimilarQuestions: false,
      similarQuestions: [],
      suggestionMessage: "",
    });

  // Cache states
  const [hasCachedData, setHasCachedData] = useState(false);
  const [showCacheNotification, setShowCacheNotification] = useState(false);

  useEffect(() => {
    if (!isAuthenticated) {
      navigate("/login", { state: { from: { pathname: "/ask" } } });
      return;
    }
    loadTags();
    loadCachedData();
  }, [isAuthenticated, navigate]);

  // Auto-save effect
  useEffect(() => {
    if (formData.title || formData.body || formData.tags.length > 0) {
      cacheService.autoSave(formData);
    }
  }, [formData]);

  // Warn user about unsaved changes
  useEffect(() => {
    const handleBeforeUnload = (e: BeforeUnloadEvent) => {
      if (formData.title || formData.body || formData.tags.length > 0) {
        e.preventDefault();
        e.returnValue =
          "You have unsaved changes. Are you sure you want to leave?";
        return "You have unsaved changes. Are you sure you want to leave?";
      }
    };

    window.addEventListener("beforeunload", handleBeforeUnload);
    return () => window.removeEventListener("beforeunload", handleBeforeUnload);
  }, [formData]);

  // Load cached data
  const loadCachedData = () => {
    const cachedData = cacheService.loadDraft();
    if (cachedData) {
      setFormData(cachedData);
      setHasCachedData(true);
      setShowCacheNotification(true);
      // Auto-hide notification after 5 seconds
      setTimeout(() => setShowCacheNotification(false), 5000);
    }
  };

  const loadTags = async () => {
    try {
      const tags = await tagService.getAllTags();
      setAvailableTags(tags);
    } catch (error) {
      console.error("Error loading tags:", error);
    }
  };

  const validateForm = (): boolean => {
    const newErrors: FormErrors = {};

    if (!formData.title.trim()) {
      newErrors.title = "Title is required";
    } else if (formData.title.length < 10) {
      newErrors.title = "Title must be at least 10 characters";
    }

    if (!formData.body.trim()) {
      newErrors.body = "Question body is required";
    } else if (formData.body.length < 30) {
      newErrors.body = "Question body must be at least 30 characters";
    }

    if (formData.tags.length === 0) {
      newErrors.tags = "At least one tag is required";
    } else if (formData.tags.length > 5) {
      newErrors.tags = "Maximum 5 tags allowed";
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setSubmitError("");

    if (!validateForm()) return;

    // Check for similar questions first
    await checkSimilarity();
  };

  const checkSimilarity = async () => {
    setIsCheckingSimilarity(true);
    try {
      const result = await geminiService.checkSimilarity(formData);
      setSimilarQuestionsData(result);
      setShowSimilarQuestions(true);
    } catch (error: any) {
      console.error("Error checking similarity:", error);
      // If similarity check fails, proceed with posting
      await createQuestion();
    } finally {
      setIsCheckingSimilarity(false);
    }
  };

  const createQuestion = async () => {
    setIsLoading(true);
    try {
      const question = await questionService.createQuestion(formData);
      // Clear cache after successful submission
      cacheService.clearDraft();
      navigate(`/questions/${question.id}`);
    } catch (error: any) {
      setSubmitError(
        error.message || "Failed to create question. Please try again."
      );
    } finally {
      setIsLoading(false);
    }
  };

  const handleProceedAnyway = () => {
    setShowSimilarQuestions(false);
    createQuestion();
  };

  const handleDismissSimilarity = () => {
    setShowSimilarQuestions(false);
  };

  const handleChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>
  ) => {
    const { name, value } = e.target;
    setFormData((prev) => ({ ...prev, [name]: value }));

    // Clear error when user starts typing
    if (errors[name]) {
      setErrors((prev) => ({ ...prev, [name]: "" }));
    }
  };

  const handleTagAdd = (tagName: string) => {
    const trimmedTag = tagName.trim().toLowerCase();
    if (
      trimmedTag &&
      !formData.tags.includes(trimmedTag) &&
      formData.tags.length < 5
    ) {
      setFormData((prev) => ({
        ...prev,
        tags: [...prev.tags, trimmedTag],
      }));
      setTagInput("");
      setShowTagSuggestions(false);

      // Clear tags error
      if (errors.tags) {
        setErrors((prev) => ({ ...prev, tags: "" }));
      }
    }
  };

  const handleTagRemove = (tagToRemove: string) => {
    setFormData((prev) => ({
      ...prev,
      tags: prev.tags.filter((tag) => tag !== tagToRemove),
    }));
  };

  const handleTagInputKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === "Enter" || e.key === ",") {
      e.preventDefault();
      if (tagInput.trim()) {
        handleTagAdd(tagInput);
      }
    }
  };

  const filteredTags = availableTags.filter(
    (tag) =>
      tag.name.toLowerCase().includes(tagInput.toLowerCase()) &&
      !formData.tags.includes(tag.name.toLowerCase())
  );

  if (!isAuthenticated) {
    return null; // Will redirect in useEffect
  }

  return (
    <>
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        exit={{ opacity: 0, y: -20 }}
        transition={{ duration: 0.3 }}
        className="max-w-4xl mx-auto"
      >
        {/* Cache notification */}
        {showCacheNotification && (
          <motion.div
            initial={{ opacity: 0, y: -20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -20 }}
            className="mb-4 bg-blue-50 border border-blue-200 text-blue-700 px-4 py-3 rounded-lg"
          >
            <div className="flex items-center justify-between">
              <span>📝 Your previous draft has been restored!</span>
              <button
                onClick={() => setShowCacheNotification(false)}
                className="text-blue-600 hover:text-blue-800 font-bold"
              >
                ×
              </button>
            </div>
          </motion.div>
        )}

        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-8">
            Ask a Question
          </h1>

          <form onSubmit={handleSubmit} className="space-y-6">
            {submitError && (
              <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg">
                {submitError}
              </div>
            )}

            {/* Title */}
            <div>
              <label
                htmlFor="title"
                className="block text-sm font-medium text-gray-700 mb-2"
              >
                Title
              </label>
              <p className="text-sm text-gray-500 mb-3">
                Be specific and imagine you're asking a question to another
                person
              </p>
              <input
                id="title"
                name="title"
                type="text"
                value={formData.title}
                onChange={handleChange}
                className={`w-full px-3 py-2 border rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-transparent ${
                  errors.title ? "border-red-300" : "border-gray-300"
                }`}
                placeholder="e.g. How do I implement authentication in React?"
              />
              {errors.title && (
                <p className="mt-1 text-sm text-red-600">{errors.title}</p>
              )}
            </div>

            {/* Body */}
            <div>
              <label
                htmlFor="body"
                className="block text-sm font-medium text-gray-700 mb-2"
              >
                Body
              </label>
              <p className="text-sm text-gray-500 mb-3">
                Include all the information someone would need to answer your
                question
              </p>
              <textarea
                id="body"
                name="body"
                rows={12}
                value={formData.body}
                onChange={handleChange}
                className={`w-full px-3 py-2 border rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-transparent ${
                  errors.body ? "border-red-300" : "border-gray-300"
                }`}
                placeholder="Describe your problem in detail. Include what you've tried and what you expected to happen."
              />
              {errors.body && (
                <p className="mt-1 text-sm text-red-600">{errors.body}</p>
              )}
            </div>

            {/* Tags */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Tags
              </label>
              <p className="text-sm text-gray-500 mb-3">
                Add up to 5 tags to describe what your question is about
              </p>

              {/* Selected Tags */}
              {formData.tags.length > 0 && (
                <div className="flex flex-wrap gap-2 mb-3">
                  {formData.tags.map((tag) => (
                    <span
                      key={tag}
                      className="inline-flex items-center px-3 py-1 text-sm bg-blue-100 text-blue-800 rounded-full"
                    >
                      {tag}
                      <button
                        type="button"
                        onClick={() => handleTagRemove(tag)}
                        className="ml-2 text-blue-600 hover:text-blue-800"
                      >
                        ×
                      </button>
                    </span>
                  ))}
                </div>
              )}

              {/* Tag Input */}
              <div className="relative">
                <input
                  type="text"
                  value={tagInput}
                  onChange={(e) => {
                    setTagInput(e.target.value);
                    setShowTagSuggestions(true);
                  }}
                  onKeyDown={handleTagInputKeyDown}
                  onFocus={() => setShowTagSuggestions(true)}
                  onBlur={() =>
                    setTimeout(() => setShowTagSuggestions(false), 200)
                  }
                  className={`w-full px-3 py-2 border rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-transparent ${
                    errors.tags ? "border-red-300" : "border-gray-300"
                  }`}
                  placeholder="e.g. javascript, react, authentication"
                  disabled={formData.tags.length >= 5}
                />

                {/* Tag Suggestions */}
                {showTagSuggestions && tagInput && filteredTags.length > 0 && (
                  <div className="absolute z-10 w-full mt-1 bg-white border border-gray-300 rounded-lg shadow-lg max-h-40 overflow-y-auto">
                    {filteredTags.slice(0, 10).map((tag) => (
                      <button
                        key={tag.id}
                        type="button"
                        onClick={() => handleTagAdd(tag.name)}
                        className="w-full text-left px-3 py-2 hover:bg-gray-100 transition-colors duration-200"
                      >
                        {tag.name}
                      </button>
                    ))}
                  </div>
                )}
              </div>

              {errors.tags && (
                <p className="mt-1 text-sm text-red-600">{errors.tags}</p>
              )}
            </div>

            {/* Submit Button */}
            <div className="flex items-center justify-between pt-6">
              <button
                type="button"
                onClick={() => navigate("/")}
                className="px-6 py-3 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors duration-200"
              >
                Cancel
              </button>

              <motion.button
                whileHover={{ scale: 1.02 }}
                whileTap={{ scale: 0.98 }}
                type="submit"
                disabled={isLoading || isCheckingSimilarity}
                className="px-8 py-3 bg-orange-500 text-white rounded-lg hover:bg-orange-600 disabled:opacity-50 disabled:cursor-not-allowed transition-colors duration-200"
              >
                {isCheckingSimilarity ? (
                  <div className="flex items-center">
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                    Checking for similar questions...
                  </div>
                ) : isLoading ? (
                  <div className="flex items-center">
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                    Posting...
                  </div>
                ) : (
                  "Post Your Question"
                )}
              </motion.button>
            </div>
          </form>
        </div>
      </motion.div>

      {/* Similar Questions Modal */}
      <SimilarQuestionsSuggestion
        isVisible={showSimilarQuestions}
        isLoading={isCheckingSimilarity}
        similarQuestions={similarQuestionsData.similarQuestions}
        suggestionMessage={similarQuestionsData.suggestionMessage}
        onDismiss={handleDismissSimilarity}
        onProceedAnyway={handleProceedAnyway}
      />
    </>
  );
};

export default AskQuestion;
