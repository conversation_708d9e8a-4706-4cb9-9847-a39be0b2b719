import { Tag } from "../types";
import { mockApiService } from "./mockApiService";

// Always use real API
const USE_MOCK_API = false;

export const tagService = {
  async getAllTags(): Promise<Tag[]> {
    if (USE_MOCK_API) {
      return mockApiService.getAllTags();
    }
    // Real API implementation
    const { apiClient } = await import("./api");
    return apiClient.get<Tag[]>("/tags");
  },

  async searchTags(query: string): Promise<Tag[]> {
    if (USE_MOCK_API) {
      return mockApiService.searchTags(query);
    }
    // Real API implementation
    const { apiClient } = await import("./api");
    return apiClient.get<Tag[]>("/tags", { search: query });
  },
};
